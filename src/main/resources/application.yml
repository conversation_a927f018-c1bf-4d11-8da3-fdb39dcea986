server:
  port: 19001
spring:
  application:
    name: openles
  #resources:
  #static-locations: classpath:/static/
  datasource:
    #url: *****************************************
    #username: admin
    #url: ****************************************************************************************************************************************************************
    url: jdbc:h2:file:./db/areasignal;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=false
    username: root
    password: system
    #driverClassName: oracle.jdbc.driver.OracleDriver
  #    type: com.alibaba.druid.pool.DruidDataSource
  jpa:
    # Specify the DBMS
    #database: ORACLE
    database: h2
    # Show or not log for each sql query
    show-sql: true
    #open-in-view: true
    hibernate:
      # Hibernate ddl auto (create, create-drop, update)
      ddl-auto: update
      # Naming strategy
      #naming-strategy: org.hibernate.cfg.ImprovedNamingStrategy
      #hibernate5配置
    properties:
      hibernate:
        #dialect: org.hibernate.dialect.Oracle10gDialect
        cache:
          # 打开二级缓存
          use_second_level_cache: false
        show_sql: false
        use_sql_comments: true
        format_sql: true
  data:
    rest:
      base-path: /api
    redis:
      host: ***************
      port: 6379
      database: 13
      password: qwert12345!@#$%
  rabbitmq:
    host: ***************
    port: 5672
    username: admin
    password: admin
    virtual-host: /
    listener:
      simple:
        prefetch: 200

  task:
    scheduling:
      pool:
        size: 4
  h2:
    console:
      enabled: true
      path: /h2
      settings:
        web-allow-others: true
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
logging:
  level:
    com.myweb: debug
    com.les: debug
    io.netty: error
  file:
    size: 100MB
    path: ./daa-log
  logback:
    rolling-policy:
      max-history: 7
      max-file-size: 100MB

global:
  areaNo: 12
  mq:
    exchange:
      name: openles.topic
    centralExchange:
      name: openles.direct
      queue: area-openles-${global.areaNo}
      routingPrefix: All.IP.PARAM
    exchange2:
      # 信号数据发送exchange
      name: adapter-openles
      routingKeyPrefix: adapter.openles.area.
      routingKeyPrefixListen: adapter.openles.system.
      cmdQueue: adapter-openles-${global.areaNo}
  cityCode: 321200
  departmentCode:
  #ops的访问地址
  opsip: *************:80
  #是否启用sgp的数据存储
  enableUseSgp: true
  #signal-param数据地址
  signalParamIp: ***************/sgp
  #**************:8232
  #是否启动信号机、路口数据项数据校验更新
  enableCheckSignalAndCrossingData: true
  #是否针对信号品牌进行过滤
  brandList:
  # area区域号过滤
  areaList: 12
  # 默认系统步进等最大控制时间
  maxSystemControlTime: 3600

#### 手动配置的socket连接参数
its:
  server:
    local:
      #监听类型
      listens:
        port: 7301
        protocol: OPENLES
      connect:
        ip:
        port:
        localPort:
        protocol:
      comm:
        address:
        protocol:
  juncer:
    # 不进行记录的参数项
    disableLogMsgs:
      #心跳数据项
      - 0x01020000
      - 0x01020100
      - 0x01030000
      - 0x01030100
      #秒级状态数据项
      - 0x03030100
      - 0x03030200
      - 0x03030300
    # 不需要序列化的日志字段
    disableFields:
      - "msgType"
      - "msgTypeCode"
      - "msgTypeCodeHex"

#### 调试配置参数,最大支持3个测试信号机配置
test:
  useTest: true
  signal-map:
    signal:
      signalId: CC00010
      crossingIds: 320100JC00011
      noArea: 12
      noJunc: 1
      subJuncNos: 1
      ip: **************
      port: 3000
    signal2:
      signalId: CC00020
      crossingIds: 320100JC00021
      noArea: 12
      noJunc: 2
      subJuncNos: 1
      ip: **************
      port: 3000
    signal3:
      signalId: CC00030
      crossingIds: 320100JC00031
      noArea: 12
      noJunc: 3
      subJuncNos: 1
      ip: **************
      port: 3000
app:
  locale:
    # 默认语言设置
    # 可选值：zh_CN（简体中文）, zh_TW（繁体中文）, en（英文）
    default-locale: zh_CN
  token:
    # 是否开启token验证
    check: false