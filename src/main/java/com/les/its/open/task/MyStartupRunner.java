package com.les.its.open.task;

import com.les.its.open.area.juncer.proc.ControllerAgentService;
import com.les.its.open.area.message.handler.status.TabControlModeHandler;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.CrossingService;
import com.les.its.open.area.message.service.SignalCacheService;
import com.les.its.open.area.net.proc.basic.InMsgService;
import com.les.its.open.area.net.proc.basic.OutMsgService;
import com.les.its.open.bussiness.service.LocalSignalCacheService;
import com.les.its.open.config.ITSSocketConfigure;
import com.les.its.open.config.TestSignalConfigure;
import com.les.its.open.netty.link.ClientType;
import com.les.its.open.netty.link.LinkManager;
import com.les.its.open.netty.link.ServerType;
import com.les.its.open.utils.ConstValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 系统初始化操作类
 * @date 2014年3月15日
 */
@Component
@Order(value = 2)
@Slf4j
public class MyStartupRunner implements CommandLineRunner {

    @Autowired
    private LocalSignalCacheService localSignalCacheService;

    @Autowired
    private ControllerService controllerService;

    @Autowired
    private CrossingService crossingService;

    @Autowired
    private TestSignalConfigure testSignalConfigure;

    @Autowired
    private SignalCacheService signalCacheService;

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Autowired
    private LinkManager linkManager;

    @Autowired
    private ControllerAgentService controllerAgentService;

    @Autowired
    private TabControlModeHandler tabControlModeHandler;

    @Autowired
    private InMsgService inMsgService;

    @Autowired
    private OutMsgService outMsgService;

    public static LocalDateTime APP_START_TIME = LocalDateTime.now();

    /**
     * 初始化开始链路管理
     */
    public void initSocket(){

        log.error(">>>step10-开始监听类协议监听");
        if (ITSSocketConfigure.listenPortList.size() != ITSSocketConfigure.listenProtocolList.size()) {
            // 修改选中的日志提示为中文
            log.error("注意：监听端口数量和协议数量不匹配！");
        } else {
            /**添加从数据库中读取的数据项*/
            List<ServerType.PortPotocol> serverList = linkManager.getServerList();
            serverList.forEach(
                    server ->
                    {
                        ITSSocketConfigure.listenPortList.add(server.getPort());
                        ITSSocketConfigure.listenProtocolList.add(server.getProtocol());
                    }
            );

            for (int i = 0; i < ITSSocketConfigure.listenPortList.size(); i++) {
                if (i < ITSSocketConfigure.listenProtocolList.size()) {
                    log.error("在端口{}加载协议{} ", ITSSocketConfigure.listenPortList.get(i),
                            ITSSocketConfigure.listenProtocolList.get(i));
                    asyncTaskService.executeServerAsyncTask(ITSSocketConfigure.listenPortList.get(i),
                            ITSSocketConfigure.listenProtocolList.get(i));
                }
            }
        }

        log.error(">>>step11-服务启动执行，连接远程端口协议加载");
        if ((ITSSocketConfigure.connectIPList.size() != ITSSocketConfigure.connectPortList.size())
                || (ITSSocketConfigure.connectIPList.size() != ITSSocketConfigure.connectProtocolList.size())) {
            log.error("configured connect ip、port、protocol not equal ... ip =[{}] port=[{}] protocol=[{}]",
                    ITSSocketConfigure.connectIPList, ITSSocketConfigure.connectPortList, ITSSocketConfigure.connectProtocolList);
        } else {

            /*添加从数据库中读取的数据项*/
            List<ClientType.IpPortProtocol> clientList = linkManager.getClientList();
            clientList.stream().filter(
                    client -> client.getIp().matches(ConstValue.IP_PATTERN)
            ).forEach(
                    client ->
                    {
                        ITSSocketConfigure.connectIPList.add(client.getIp());
                        ITSSocketConfigure.connectPortList.add(client.getPort());
                        ITSSocketConfigure.connectLocalPortList.add(client.getLocalPort());
                        ITSSocketConfigure.connectProtocolList.add(client.getProtocol());
                    }
            );

            for (int i = 0; i < ITSSocketConfigure.connectIPList.size(); i++) {
                log.error("远程地址{}端口{}加载协议{} ", ITSSocketConfigure.connectIPList.get(i),
                        ITSSocketConfigure.connectPortList.get(i), ITSSocketConfigure.connectProtocolList.get(i));
                asyncTaskService.executeClientAsyncTask(ITSSocketConfigure.connectIPList.get(i),
                        ITSSocketConfigure.connectPortList.get(i), ITSSocketConfigure.connectLocalPortList.get(i),
                        ITSSocketConfigure.connectProtocolList.get(i));
            }
        }

        log.error(">>>step12-服务启动执行，串口协议加载");
        if ((ITSSocketConfigure.commProtocolList.size() != ITSSocketConfigure.commAddressList.size())) {
            log.error("注意：配置的串口数量=[{}]和协议=[{}]数量不匹配 ",
                    ITSSocketConfigure.commAddressList, ITSSocketConfigure.commProtocolList);
            return;
        } else {
            /**添加从数据库中读取的数据项*/
            List<ClientType.IpPortProtocol> clientList = linkManager.getClientList();
            clientList.stream().filter(
                    client -> !client.getIp().matches(ConstValue.IP_PATTERN)
            ).forEach(
                    client ->
                    {
                        ITSSocketConfigure.commAddressList.add(client.getIp());
                        ITSSocketConfigure.commProtocolList.add(client.getProtocol());
                    }
            );

            for (int i = 0; i < ITSSocketConfigure.commAddressList.size(); i++) {
                log.error("串口地址{}加载协议{} ", ITSSocketConfigure.commAddressList,
                        ITSSocketConfigure.commProtocolList.get(i));

                asyncTaskService.executeCommtAsyncTaskRandomDelay(ITSSocketConfigure.commAddressList.get(i),
                        ITSSocketConfigure.commProtocolList.get(i));
            }
        }

    }

    @Override
    public void run(String... args) throws Exception {

        log.error(">>>>>>>>>>>>>>>服务启动执行，执行数据准备<<<<<<<<<<<<");
        log.error(">>>step1-从数据库中加载数据项");
        /*基础内存数据*/
        localSignalCacheService.loadDataFromDb();
        signalCacheService.loadDataFromDb();

        /*信号机路口数据**/
        if (!testSignalConfigure.isUseTest()) {
            log.error(">>>step1-从sgp读取信号机以及路口数据项");
            try {
                controllerService.getControllerFromSgp();
                crossingService.getCrossingFromSgp();
            } catch (Exception e) {
                log.error("step1-初始化异常", e);
            }
        }

        {
            log.error(">>>step2-内存控制初始化");
            controllerAgentService.init();
        }

        //初始化协议转换
        {
            log.error(">>>step3-协议转换初始化");
            inMsgService.init();
            outMsgService.init();
        }

        //初始化通知所有信号机离线
        {
            log.error(">>>step4-初始化通知所有信号机离线");
            tabControlModeHandler.notifyAllLinkOff();
        }

        //启动端口服务
        log.error(">>>step10-启动链路服务");
        initSocket();

        // 打印启动成功的像素艺术标志
        printStartupSuccessLogo();
    }

    /**
     * 打印启动成功的像素艺术标志
     */
    private void printStartupSuccessLogo() {
        StringBuilder logo = new StringBuilder();
        logo.append("\n");
        logo.append("████████████████████████████████████████████████████████████████████████████████████\n");
        logo.append("██                                                                                ██\n");
        logo.append("██    ███████  ██    ██   ██████   ██████  ███████  ███████  ███████  ██  ██      ██\n");
        logo.append("██    ██       ██    ██  ██       ██       ██       ██       ██       ██  ██      ██\n");
        logo.append("██    ███████  ██    ██  ██       ██       █████    ███████  ███████  ██  ██      ██\n");
        logo.append("██         ██  ██    ██  ██       ██       ██            ██       ██  ██  ██      ██\n");
        logo.append("██    ███████   ██████    ██████   ██████  ███████  ███████  ███████  ██  ██      ██\n");
        logo.append("██                                                                                ██\n");
        logo.append("██                          ✓ 系统启动成功！                                         ██\n");
        logo.append("██                          ✓ System Started Successfully!                        ██\n");
        logo.append("██                                                                                ██\n");
        logo.append("██    ┌─────────────────────────────────────────────────────────────────────┐     ██\n");
        logo.append("██    │                        启动信息 / Startup Info                        │     ██\n");
        logo.append("██    ├─────────────────────────────────────────────────────────────────────┤     ██\n");
        logo.append("██    │  启动时间 / Start Time: " + String.format("%-35s", APP_START_TIME.toString()) + "│            ██\n");
        logo.append("██    │  应用名称 / Application: OpenLES Traffic Control System              │      ██\n");
        logo.append("██    │  版本信息 / Version:     v1.0.0                                      │      ██\n");
        logo.append("██    │  运行状态 / Status:      🟢 RUNNING                                   │    ██\n");
        logo.append("██    └─────────────────────────────────────────────────────────────────────┘     ██\n");
        logo.append("██                                                                                ██\n");
        logo.append("██    🚦 交通信号控制系统已就绪 - Traffic Signal Control System Ready                 ██\n");
        logo.append("██    🌐 网络服务已启动 - Network Services Started                                   ██\n");
        logo.append("██    📡 协议转换已初始化 - Protocol Conversion Initialized                          ██\n");
        logo.append("██    💾 数据缓存已加载 - Data Cache Loaded                                          ██\n");
        logo.append("██                                                                                 ██\n");
        logo.append("████████████████████████████████████████████████████████████████████████████████████\n");

        // 使用 System.out.println 确保在控制台显示，不受日志级别影响
        System.out.println(logo.toString());

        // 同时记录到日志文件
        log.error("=".repeat(80));
        log.error("🎉 OpenLES 交通信号控制系统启动成功！");
        log.error("🎉 OpenLES Traffic Signal Control System Started Successfully!");
        log.error("启动时间: {}", APP_START_TIME);
        log.error("系统状态: 运行中 (RUNNING)");
        log.error("=".repeat(80));
    }
}
