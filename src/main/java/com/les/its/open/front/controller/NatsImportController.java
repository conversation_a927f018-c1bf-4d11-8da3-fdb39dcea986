package com.les.its.open.front.controller;


import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.les.its.open.area.message.MqMessageProcess;
import com.les.its.open.area.message.MqMsgSimSyncProcess;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.net.proc.TabCmdService;
import com.les.its.open.bussiness.service.rtt.RTTService;
import com.les.its.open.front.controller.dto.DataImportDto;
import com.les.its.open.front.controller.dto.ImportData;
import com.les.its.open.front.controller.dto.PwdDto;
import com.les.its.open.front.websocket.service.WsMessageService;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

@RestController
@RequestMapping("openles")
@Slf4j
public class NatsImportController {

    private final WsMessageService wsMessageService;

    private final MqMessageProcess mqMessageProcess;

    private final TabCmdService tabCmdService;

    private final MqMsgSimSyncProcess mqMsgSimSyncProcess;

    private final RTTService rttService;

    /**
     * 生成的前端序列号
     */
    private final AtomicLong seqAtomicLong = new AtomicLong(0);

    /**
     * 待加载的数据项
     */
    private final Map<String, List<String>> toDownloadMap = new ConcurrentHashMap<>();

    /**
     * 当前解析的序列号
     */
    private String currentSeq = "";

    public NatsImportController(WsMessageService wsMessageService,
                             MqMessageProcess mqMessageProcess,
                             TabCmdService tabCmdService,
                             MqMsgSimSyncProcess mqMsgSimSyncProcess,
                             RTTService rttService) {
        this.wsMessageService = wsMessageService;
        this.mqMessageProcess = mqMessageProcess;
        this.tabCmdService = tabCmdService;
        this.mqMsgSimSyncProcess = mqMsgSimSyncProcess;
        this.rttService = rttService;
    }


    /**
     * 验证密码
     * @param controllerId
     * @return
     */
    @PostMapping("/valid/{controllerId}")
    public JsonResult<?> valid(@PathVariable String controllerId, @RequestBody PwdDto pwdDto) {

        if(!pwdDto.getPassword().equalsIgnoreCase("4434117" + controllerId)){
            return new JsonResult<>(false, "50000", "密码错误", "");
        }

        return new JsonResult<>(true, "20000", "验证成功", pwdDto);
    }

    public String getSeq() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        String nowStr = now.format(format);
        return nowStr + String.format("%03d", seqAtomicLong.getAndIncrement() % 100);
    }


    /**
     * 前端数据上传
     *
     * @param file
     * @return
     * @throws IOException
     */
    @PostMapping("/nats/upload/{controllerId}")
    public JsonResult<?> upload(@PathVariable String controllerId,
                             @RequestParam("file") MultipartFile file) throws IOException {
        // 获取文件名
        String fileName = file.getOriginalFilename();
        log.info("接收到文件上传请求: {}, 控制器ID: {}", fileName, controllerId);

        // 读取文件全部内容为字符串
        String content = new String(file.getBytes(), StandardCharsets.UTF_8);

        List<Map<String, List<String>>> dataMapList = JSONObject.parseObject(content,
                new TypeReference<>() {
        });

        List<ImportData> lists = new ArrayList<>();

        dataMapList.stream().forEach(
                dataItem -> {
                    dataItem.forEach(
                            (key, value) -> {
                                String[] datas = key.split("-");
                                if(datas.length == 3){
                                    lists.add(ImportData.builder().id(datas[0]).name(datas[2]).build());

                                    toDownloadMap.put(datas[0], value);
                                }
                            }
                    );
                }
        );

        DataImportDto dataImportDto = DataImportDto.builder()
                .transactionId(getSeq())
                .list(lists).build();

        currentSeq = dataImportDto.getTransactionId();

        return new JsonResult<>(true, "20000", "解析导入数据成功", dataImportDto);
    }


    /**
     * 参数加载
     */
    @PostMapping("/nats/download/{controllerId}/{transactionId}/{paramId}")
    public JsonResult<?> upload(@PathVariable String controllerId,
                                @PathVariable String transactionId,
                                @PathVariable String paramId)
    {
        if(!currentSeq.equalsIgnoreCase(transactionId)){
            return new JsonResult<>(false, "50000", "异常的交易编号", "");
        }

        //查找数据项
        List<String> datas = toDownloadMap.get(paramId);
        if(datas == null || datas.isEmpty()){
            return new JsonResult<>(false, "50000", "没有找到对应的数据项", "");
        }

        log.error("准备导入信号机-{}-{}-{}", controllerId, transactionId, paramId);

        Optional<Object> natsParam = mqMsgSimSyncProcess.setNatsParam(controllerId, paramId, datas);
        if(natsParam.isPresent() && natsParam.get() instanceof MqMessage mqMessage){
            if(mqMessage.getErrorCode().equalsIgnoreCase("0")){
                return new JsonResult<>(true, "20000", "导入成功", "");
            }else{
                return new JsonResult<>(false, "50000", mqMessage.getErrorInfo(), "");
            }
        }
        return new JsonResult<>(false, "50000", "导入异常", "");
    }
}
