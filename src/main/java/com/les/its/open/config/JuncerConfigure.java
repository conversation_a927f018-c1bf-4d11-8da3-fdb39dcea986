package com.les.its.open.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/6/15 13:51
 */
@Data
@Component
@ConfigurationProperties(prefix = "its.juncer")
@Slf4j
public class JuncerConfigure {

    /**
     * 不需要打印日志的消息项
     */
    private List<Integer> disableLogMsgs;

    /**
     * 不需要打印日志的字段
     */
    private List<String> disableFields;
}
