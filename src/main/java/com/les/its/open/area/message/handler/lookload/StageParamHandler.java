package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Stage;
import com.les.its.open.area.juncer.msg.param.lookload.dto.StagePhase;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.param.lookload.StageParam;
import com.les.its.open.area.message.param.lookload.sub.StageParamInfo;
import com.les.its.open.area.message.param.lookload.sub.StagePhaseParam;
import com.les.its.open.area.message.param.lookload.sub.StartupStage;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

@Component
@Slf4j
public class StageParamHandler implements MqMsgBaseHandler {

    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public StageParamHandler(ControllerService controllerService, OpenLesSender openLesSender, DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }

    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_StageParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class<?> dataType() {
        return StageParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        // 调看阶段信息
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_STAGE, 1, ParamMsgType.PARAM_STAGE.getMax(), signalInfoOp.get());
            areaMessages.add(msg);
        }

        // 调看阶段启动信息
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_STARTUP_STAGE, 1, ParamMsgType.PARAM_STARTUP_STAGE.getMax(), signalInfoOp.get());
            areaMessages.add(msg);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        //构建返回数据项
        {
            StageParam stageParam = new StageParam();
            //设置信号机编号
            stageParam.setSignalControllerID(requestMessage.getSignalControllerID());


            List<StageParamInfo> stageParamInfos = new ArrayList<>();
            stageParam.setStageParams(stageParamInfos);

            List<StartupStage> startupStages = new ArrayList<>();
            stageParam.setStartupStages(startupStages);

            //阶段启动参数
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.StartupStage> startupStagesOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_STARTUP_STAGE);
            if(startupStagesOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_STARTUP_STAGE);
                return Optional.empty();
            }
            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), startupStagesOp.get());


            //阶段信息
            Optional<List<com.les.its.open.area.juncer.msg.param.lookload.dto.Stage>> stagesOp
                    = OpenLesMqUtils.getDatas(itemDatas, ParamMsgType.PARAM_STAGE);
            if(stagesOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_STAGE);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), stagesOp.get());

            for (Stage stage : stagesOp.get()) {
                StageParamInfo stageParamInfo = new StageParamInfo();
                stageParamInfos.add(stageParamInfo);

                stageParamInfo.setStageNo(stage.getStageNo());

                // 结束手动运行期时插入阶段需求
                if(stage.getStageNo() <= startupStagesOp.get().getDemandsInsertLeavingManualAndFixStages().size()
                        && stage.getStageNo() > 0 ){
                    stageParamInfo.setDemandsInsertLeavingManualAndFixStage(
                            startupStagesOp.get().getDemandsInsertLeavingManualAndFixStages().get(stage.getStageNo() - 1));
                }else {
                    stageParamInfo.setDemandsInsertLeavingManualAndFixStage(0);
                }

                // 启动时插入阶段需求
                if(stage.getStageNo() <= startupStagesOp.get().getDemandsInsertStartUpStages().size()
                        && stage.getStageNo() > 0 ){
                    stageParamInfo.setDemandsInsertStartUpStage(
                            startupStagesOp.get().getDemandsInsertStartUpStages().get(stage.getStageNo() - 1));
                }else{
                    stageParamInfo.setDemandsInsertStartUpStage(0);
                }

                // 窗口时间
                if(stage.getStageNo() <= startupStagesOp.get().getWindowsTimes().size()
                        && stage.getStageNo() > 0 ){
                    stageParamInfo.setWindowsTime(
                            startupStagesOp.get().getWindowsTimes().get(stage.getStageNo() - 1));
                }else{
                    stageParamInfo.setWindowsTime(0);
                }

                // 无条件默认需求
                if(stage.getStageNo() <= startupStagesOp.get().getUnconditionalDemands().size()
                        && stage.getStageNo() > 0 ){
                    stageParamInfo.setUnconditionalDemand(
                            startupStagesOp.get().getUnconditionalDemands().get(stage.getStageNo() - 1));
                }else {
                    stageParamInfo.setUnconditionalDemand(0);
                }

                // 紧急调用结束后插入需求
                if(stage.getStageNo() <= startupStagesOp.get().getDemandsInsertLeavingHurryCalls().size()
                        && stage.getStageNo() > 0 ){
                    stageParamInfo.setDemandsInsertLeavingHurryCall(
                            startupStagesOp.get().getDemandsInsertLeavingHurryCalls().get(stage.getStageNo() - 1));
                } else {
                    stageParamInfo.setDemandsInsertLeavingHurryCall(0);
                }

                // 中心控制结末后插入需求
                if(stage.getStageNo() <= startupStagesOp.get().getDemandsInsertLeavingSystems().size()
                        && stage.getStageNo() > 0 ){
                    stageParamInfo.setDemandsInsertLeavingSystem(
                            startupStagesOp.get().getDemandsInsertLeavingSystems().get(stage.getStageNo() - 1));
                } else {
                    stageParamInfo.setDemandsInsertLeavingSystem(0);
                }

                // 阶段名称
                stageParamInfo.setStageName(stage.getStageName());

                //阶段相位信息
                List<StagePhaseParam> stagePhaseParams = new ArrayList<>();
                stageParamInfo.setStagePhaseParams(stagePhaseParams);
                // 相位信息
                for (int i = 0; i < stage.getStagePhases().size(); i++) {
                    StagePhase stagePhase = stage.getStagePhases().get(i);
                    {
                        //阶段不关联相位
                        if(stagePhase.getDemand() == 0){
                            continue;
                        }

                        StagePhaseParam stagePhaseParam = new StagePhaseParam();
                        stagePhaseParam.setPhaseNo(i + 1);
                        stagePhaseParam.setDemand(stagePhase.getDemand());
                        stagePhaseParam.setLaggingTime(stagePhase.getLaggingTime());
                        stagePhaseParam.setDelayCutOffTime(stagePhase.getDelayCutOffTime());
                        stagePhaseParams.add(stagePhaseParam);
                    }
                }

            }

            for (int i = 0; i < startupStagesOp.get().getStartupStages().size(); i++) {
                StartupStage startupStage = new StartupStage();
                startupStage.setCrossingSeqNo(i + 1);
                startupStage.setStartupStage(startupStagesOp.get().getStartupStages().get(i));
                startupStage.setNoDemandStage(startupStagesOp.get().getNoDemandStages().get(i));
                startupStages.add(startupStage);
            }

            //应答参数
            datas.add(stageParam);
        }

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {

        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        //转换格式
        JSONObject jsonObject = (JSONObject) (datas.get(0));
        StageParam stageParam = jsonObject.toJavaObject(StageParam.class);

        //对数据进行校验
        {
            StringBuilder stringBuilder = new StringBuilder();
            boolean validateData = dataValidatorFactory.validateData(stageParam, stringBuilder);
            if (!validateData) {
                log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                        stringBuilder, stageParam);
                errorMsg.append(stringBuilder);
                return Optional.empty();
            }
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        //阶段信息
        {
            List<com.les.its.open.area.juncer.msg.param.lookload.dto.Stage> stages = new ArrayList<>();

            stageParam.getStageParams().forEach(stageParamInfo -> {
                com.les.its.open.area.juncer.msg.param.lookload.dto.Stage stage
                        = new com.les.its.open.area.juncer.msg.param.lookload.dto.Stage();
                stage.setStageNo(stageParamInfo.getStageNo());
                stage.setStageName(stageParamInfo.getStageName());
                //阶段相位信息
                List<com.les.its.open.area.juncer.msg.param.lookload.dto.StagePhase> stagePhases = new ArrayList<>();
                stage.setStagePhases(stagePhases);
                //补全阶段关联相位数据项
                IntStream.rangeClosed(1, ParamMsgType.PARAM_PHASE.getMax()).forEach(
                    phaseNo -> {
                        Optional<StagePhaseParam> stagePhaseParamOptional = stageParamInfo.getStagePhaseParams().stream().filter(stagePhaseParam -> stagePhaseParam.getPhaseNo().equals(phaseNo))
                                .findAny();
                        if(stagePhaseParamOptional.isPresent()){
                                StagePhaseParam stagePhaseParam = stagePhaseParamOptional.get();
                                com.les.its.open.area.juncer.msg.param.lookload.dto.StagePhase stagePhase
                                        = new com.les.its.open.area.juncer.msg.param.lookload.dto.StagePhase();
                                stagePhase.setDemand(stagePhaseParam.getDemand());
                                stagePhase.setLaggingTime(stagePhaseParam.getLaggingTime() == null ? 0 : stagePhaseParam.getLaggingTime());
                                stagePhase.setDelayCutOffTime(stagePhaseParam.getDelayCutOffTime() == null ? 0 : stagePhaseParam.getDelayCutOffTime());
                                stagePhases.add(stagePhase);
                        }else{
                            com.les.its.open.area.juncer.msg.param.lookload.dto.StagePhase stagePhase
                                    = new com.les.its.open.area.juncer.msg.param.lookload.dto.StagePhase();
                            stagePhase.setDemand(0);
                            stagePhase.setLaggingTime(0);
                            stagePhase.setDelayCutOffTime(0);
                            stagePhases.add(stagePhase);
                        }
                });

                stages.add(stage);
            });

            //生成加载参数
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_STAGE, stages, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        //启动阶段信息
        {
            com.les.its.open.area.juncer.msg.param.lookload.dto.StartupStage startupStage
                    = new com.les.its.open.area.juncer.msg.param.lookload.dto.StartupStage();


            // 无需求进入阶段 (8个子路口配置)
            List<Integer> noDemandStage = new ArrayList<>();
            startupStage.setNoDemandStages(noDemandStage);
            // 启动进入阶段 (8个子路口配置)
            List<Integer> startupStages = new ArrayList<>();
            startupStage.setStartupStages(startupStages);
            // 结束手动定周期时插入阶段需求 (针对每个阶段)
            List<Integer> demandsInsertLeavingManualAndFixStages = new ArrayList<>();
            startupStage.setDemandsInsertLeavingManualAndFixStages(demandsInsertLeavingManualAndFixStages);
            // 启动时插入阶段需求 (针对每个阶段)
            List<Integer> demandsInsertStartUpStages = new ArrayList<>();
            startupStage.setDemandsInsertStartUpStages(demandsInsertStartUpStages);
            // 紧急调用结束后插入需求
            List<Integer> demandsInsertLeavingHurryCalls = new ArrayList<>();
            startupStage.setDemandsInsertLeavingHurryCalls(demandsInsertLeavingHurryCalls);
            // 中心控制结末后插入需求
            List<Integer> demandsInsertLeavingSystems = new ArrayList<>();
            startupStage.setDemandsInsertLeavingSystems(demandsInsertLeavingSystems);
            //无条件默认需求
            List<Integer> unconditionalDemands = new ArrayList<>();
            startupStage.setUnconditionalDemands(unconditionalDemands);
            // 窗口时间
            List<Integer> windowsTimes = new ArrayList<>();
            startupStage.setWindowsTimes(windowsTimes);

            //遍历启动参数
            IntStream.rangeClosed(1, ParamMsgType.PARAM_LANE_CHANNELIZATION_INFO.getMax()).forEach(
                    crossingNo -> {
                        Optional<StartupStage> startupStageOptional = stageParam.getStartupStages().stream().filter(
                                startupStageInfo -> startupStageInfo.getCrossingSeqNo().equals(crossingNo)
                        ).findFirst();
                        if(startupStageOptional.isPresent()){
                            StartupStage startupStageInfo = startupStageOptional.get();
                            noDemandStage.add(startupStageInfo.getNoDemandStage());
                            startupStages.add(startupStageInfo.getStartupStage());
                        }else{
                            noDemandStage.add(0);
                            startupStages.add(0);
                        }
                    }
            );

            //遍历阶段参数
            IntStream.rangeClosed(1, ParamMsgType.PARAM_STAGE.getMax()).forEach(
                    stageNo -> {

                        //当前阶段参数中是否包含此数据
                        Optional<StageParamInfo> stageParamInfoOp = stageParam.getStageParams().stream().filter(
                                stageNoParam -> stageNoParam.getStageNo().equals(stageNo)
                        ).findFirst();

                        if(stageParamInfoOp.isPresent()) {
                            StageParamInfo stageParamInfo = stageParamInfoOp.get();
                            demandsInsertLeavingManualAndFixStages.add(stageParamInfo.getDemandsInsertLeavingManualAndFixStage());
                            demandsInsertStartUpStages.add(stageParamInfo.getDemandsInsertStartUpStage());
                            demandsInsertLeavingHurryCalls.add(stageParamInfo.getDemandsInsertLeavingHurryCall());
                            demandsInsertLeavingSystems.add(stageParamInfo.getDemandsInsertLeavingSystem());
                            unconditionalDemands.add(stageParamInfo.getUnconditionalDemand());
                            windowsTimes.add(stageParamInfo.getWindowsTime());
                        }else{
                            demandsInsertLeavingManualAndFixStages.add(0);
                            demandsInsertStartUpStages.add(0);
                            demandsInsertLeavingHurryCalls.add(0);
                            demandsInsertLeavingSystems.add(0);
                            unconditionalDemands.add(0);
                            windowsTimes.add(0);
                        }
                    }
            );

            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_STARTUP_STAGE, startupStage, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}
