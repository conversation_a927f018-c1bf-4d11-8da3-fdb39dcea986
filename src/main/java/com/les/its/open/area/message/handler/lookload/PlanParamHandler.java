package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.PlanStageParamExt;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.param.lookload.PlanParam;
import com.les.its.open.area.message.param.lookload.sub.PlanStageParam;
import com.les.its.open.area.message.param.lookload.sub.PlanStagePhaseParam;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

@Component
@Slf4j
public class PlanParamHandler implements MqMsgBaseHandler {


    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public PlanParamHandler(ControllerService controllerService, OpenLesSender openLesSender, DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }

    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_PlanParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class<?> dataType() {
        return PlanParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        if(datas.size() == 1){
            int planNo = datas.get(0);
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_PLAN, planNo,
                    1, signalInfoOp.get());
            areaMessages.add(msg);
        }else {
            // 调看方案,受限于方案长度，每次最大只能调看8个方案
            final int EACH_PLAN_COUNT = ParamMsgType.PARAM_PLAN.getLookMax();
            for (int i = 0; i < ParamMsgType.PARAM_PLAN.getMax() / EACH_PLAN_COUNT; i++) {
                AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_PLAN, 1 + (EACH_PLAN_COUNT) * i,
                        EACH_PLAN_COUNT, signalInfoOp.get());
                areaMessages.add(msg);
            }
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());


        List<Integer> dataNos = new ArrayList<>();
        try {
            List<Object> objectList = requestMessage.getObjectList();
            objectList.forEach(
                    objectId -> {
                        dataNos.add((Integer) objectId);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常", e);
        }

        if(dataNos.isEmpty()){
            IntStream.rangeClosed(1, ParamMsgType.PARAM_PLAN.getMax()).forEach(dataNos::add);
        }

        Optional<List<com.les.its.open.area.juncer.msg.param.lookload.dto.Plan>> plansOp
                = OpenLesMqUtils.getDatas(itemDatas, ParamMsgType.PARAM_PLAN);
        if(plansOp.isEmpty()){
            log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_PLAN);
            return Optional.empty();
        }
        log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), plansOp.get());


        //构建返回数据项
        dataNos.forEach(dataNo -> {
            //根据编号查找数据项
            plansOp.get().stream().filter(
                    plan -> plan.getPlanNo().intValue() == dataNo
            ).findAny().ifPresent( plan ->

            //构建返回数据项
            {
                PlanParam planParam = new PlanParam();
                //设置信号机编号
                planParam.setSignalControllerID(requestMessage.getSignalControllerID());

                List<PlanStageParam> planStageParams = new ArrayList<>();
                planParam.setPlanStageParams(planStageParams);

                //方案编号
                planParam.setPlanNo(plan.getPlanNo());
                //子路口号
                planParam.setCrossingSeqNo(plan.getCrossingSeqNo());
                //周期
                planParam.setCycle(plan.getCycle());
                //协调序号
                planParam.setCoordinatedStageSeq(plan.getCoordinatedStageSeq());
                //协调参考点 (绿灯初，黄灯初，红灯初)
                planParam.setCoordinatedRef(plan.getCoordinatedRef());
                //相位差
                planParam.setOffset(plan.getOffset());
                // 阶段数据项
                {
                    for (int i = 0; i < plan.getStageNumber() &&
                            i < plan.getPlanStageParams().size(); i++) {
                       com.les.its.open.area.juncer.msg.param.lookload.dto.PlanStageParam planStageParamOrg
                               = plan.getPlanStageParams().get(i);

                        // 方案阶段参数
                        PlanStageParam planStageParam = new PlanStageParam();
                        planStageParam.setStageNo(planStageParamOrg.getStageNo());
                        planStageParam.setStageTime(planStageParamOrg.getStageTime());
                        planStageParam.setStageActivationType(planStageParamOrg.getStageActivationType());
                        planStageParam.setCoordinatedForceOff(planStageParamOrg.getCoordinatedForceOff());

                        List<PlanStagePhaseParam> planStagePhaseParams = new ArrayList<>();
                        planStageParam.setPlanStagePhaseParams(planStagePhaseParams);

                        // 相位运行早截断参数
                        for (int phaseIndex = 0; phaseIndex < planStageParamOrg.getExtParams().size(); phaseIndex++) {
                            PlanStageParamExt planStageParamExt = planStageParamOrg.getExtParams().get(phaseIndex);

                            PlanStagePhaseParam planStagePhaseParam = new PlanStagePhaseParam();
                            planStagePhaseParam.setPhaseNo(phaseIndex + 1);
                            planStagePhaseParam.setLaggingTime(planStageParamExt.getLaggingTime());
                            planStagePhaseParam.setDelayCutOffTime(planStageParamExt.getDelayCutOffTime());

                            //同时为0，认为没有配置参数
                            if(planStagePhaseParam.getLaggingTime() == 0 && planStagePhaseParam.getDelayCutOffTime() == 0){
                                continue;
                            }
                            planStagePhaseParams.add(planStagePhaseParam);
                        }

                        planStageParams.add(planStageParam);
                    }
                }
                //  方案名称
                planParam.setPlanName(plan.getPlanName());

                //应答参数
                datas.add(planParam);
            });
        });

        if(datas.isEmpty()){
            errorMsgRet.append("数据返回异常");
            return Optional.empty();
        }
        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {
        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();
        //转换格式

        List<com.les.its.open.area.juncer.msg.param.lookload.dto.Plan> planList
                = new ArrayList<>();

        final int EACH_PLAN_COUNT = ParamMsgType.PARAM_PLAN.getLookMax();

        for (int i = 0; i < datas.size(); i++) {

            if(i % EACH_PLAN_COUNT == 0){
                List<com.les.its.open.area.juncer.msg.param.lookload.dto.Plan> planListEach = new ArrayList<>();
                planListEach.addAll(planList);

                planList.clear();
                //非空数据时，每最大个数生成一个加载报文数据项
                if(!planListEach.isEmpty()){
                    //生成加载参数
                    //生成加载参数
                    AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_PLAN,
                            planListEach, signalInfoOp.get());
                    areaMessages.add(areaMessage);
                }
            }


            Object data = datas.get(i);

            JSONObject jsonObject = (JSONObject) data;
            PlanParam planParam = jsonObject.toJavaObject(PlanParam.class);
            log.error("设置参数-{}", planParam);

            //对数据进行校验
            {
                StringBuilder stringBuilder = new StringBuilder();
                boolean validateData = dataValidatorFactory.validateData(planParam, stringBuilder);
                if (!validateData) {
                    log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                            stringBuilder, planParam);
                    errorMsg.append(stringBuilder);
                    return Optional.empty();
                }
            }

            {
                com.les.its.open.area.juncer.msg.param.lookload.dto.Plan plan = new
                        com.les.its.open.area.juncer.msg.param.lookload.dto.Plan();
                planList.add(plan);
                //方案编号
                plan.setPlanNo(planParam.getPlanNo());
                //子路口号
                plan.setCrossingSeqNo(planParam.getCrossingSeqNo());
                //周期
                plan.setCycle(planParam.getCycle());
                //协调序号
                plan.setCoordinatedStageSeq(planParam.getCoordinatedStageSeq());
                //协调参考点 (绿灯初，黄灯初，红灯初)
                plan.setCoordinatedRef(planParam.getCoordinatedRef());
                //相位差
                plan.setOffset(planParam.getOffset());
                //阶段个数
                plan.setStageNumber(planParam.getPlanStageParams().size());
                List<com.les.its.open.area.juncer.msg.param.lookload.dto.PlanStageParam> planStageParams
                        = new ArrayList<>();
                plan.setPlanStageParams(planStageParams);
                //方案阶段数据
                for (int j = 0; j < planParam.getPlanStageParams().size(); j++) {
                    PlanStageParam planStageParam = planParam.getPlanStageParams().get(j);
                    {
                        com.les.its.open.area.juncer.msg.param.lookload.dto.PlanStageParam planStageParamOrg
                                = new com.les.its.open.area.juncer.msg.param.lookload.dto.PlanStageParam();
                        planStageParams.add(planStageParamOrg);
                        //阶段编号
                        planStageParamOrg.setStageNo(planStageParam.getStageNo());
                        //阶段时长
                        planStageParamOrg.setStageTime(planStageParam.getStageTime());
                        //阶段出现类型 (1:固定 2:按需)
                        planStageParamOrg.setStageActivationType(planStageParam.getStageActivationType());
                        //协调强制关闭
                        planStageParamOrg.setCoordinatedForceOff(planStageParam.getCoordinatedForceOff());
                        List<com.les.its.open.area.juncer.msg.param.lookload.dto.PlanStageParamExt> extParams
                                = new ArrayList<>();
                        planStageParamOrg.setExtParams(extParams);

                        //初始化数据项
                        IntStream.rangeClosed(1, ParamMsgType.PARAM_PHASE.getMax()).forEach(
                            phaseNo -> {
                                com.les.its.open.area.juncer.msg.param.lookload.dto.PlanStageParamExt extParam
                                        = new com.les.its.open.area.juncer.msg.param.lookload.dto.PlanStageParamExt();
                                extParam.setLaggingTime(0);
                                extParam.setDelayCutOffTime(0);
                                extParams.add(extParam);
                            }
                        );

                        //迟起早断数据更新
                        for (int k = 0; k < planStageParam.getPlanStagePhaseParams().size(); k++) {
                            PlanStagePhaseParam planStagePhaseParam = planStageParam.getPlanStagePhaseParams().get(k);

                            if(planStagePhaseParam.getPhaseNo() > 0 && planStagePhaseParam.getPhaseNo() <= extParams.size() )
                            {
                                extParams.get(planStagePhaseParam.getPhaseNo() - 1).setLaggingTime(planStagePhaseParam.getLaggingTime());
                                extParams.get(planStagePhaseParam.getPhaseNo() - 1).setDelayCutOffTime(planStagePhaseParam.getDelayCutOffTime());
                            }
                        }
                    }
                }
                //方案名称
                plan.setPlanName(planParam.getPlanName());

            }

        }

        //生成加载参数
        if(!planList.isEmpty()) {
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_PLAN,
                    planList, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage,
                                                    List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return false;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}
