package com.les.its.open.area.message;


import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.bussiness.utils.LesUtils;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.les.its.open.event.AckManager.response.ResponseMessage;
import com.les.its.open.event.AckManager.response.ResponseStatus;
import com.les.its.open.event.MessageOuterPublisher;
import com.les.its.open.protocol.InterProtocolType;
import com.les.its.open.protocol.common.InterProtocol;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 这个服务是用来进行模拟mq数据同步调看/加载的
 */
@Slf4j
@Service
public class MqMsgSimSyncProcess {


    @Autowired
    private MqMessageProcess mqMessageProcess;

    @Autowired
    private MessageOuterPublisher messageOuterPublisher;

    /**
     * 同步请求3.0数据项
     * @param signalControlId
     * @param objectId
     * @return
     */
    public Optional<Object> getNatsParam(String signalControlId, String objectId) {

        //构建信号机的查询数据项
        MqMessage mqMessage = OpenLesMqUtils.buildSimMqMsg(signalControlId, objectId);

        //构建同步等待消息
        InterProtocol interProtocol = LesUtils.buildMessage("255.255.255.255", InterProtocolType.UNKOWN_MESSAGE,
                mqMessage, true, mqMessage.getAckBackKey(), 10, false, false);

        Optional<InvokeFuture> invokeFutureOp = messageOuterPublisher.sendMessageOuter(interProtocol, false);

        if(invokeFutureOp.isPresent()) {
            //处理数据项
            mqMessageProcess.mqMessageProcess(mqMessage);
            //开始等待应答
            InvokeFuture future = invokeFutureOp.get();

            try {
                LocalDateTime startTime = LocalDateTime.now();
                log.debug("开始等待-{}应答-{}", JSONObject.toJSONString(mqMessage), startTime);
                ResponseMessage response = future.waitResponse();
                LocalDateTime endTime = LocalDateTime.now();
                long millis = Duration.between(endTime, startTime).toMillis();
                log.debug("请求数据-{} \n 收到了应答-{}, \n 花费时间-{}, \n 报文中时间-{} \n" +
                                "数据应答项-{}", JSONObject.toJSONString(mqMessage)
                        , endTime, millis,
                        System.currentTimeMillis() - response.getRequestInterProtocol().getOuterTimeStamp(), response);
                /*解析应答数据项*/
                if (response.getResponseStatus() != ResponseStatus.SUCCESS) {
                    log.error("等待{}应答失败-{}", mqMessage, response.getResponseStatus().des());
                } else {
                    log.debug("请求{}应答{}花费时间{}ms", mqMessage,
                            response.getResponseObject(), millis);
                    return Optional.of(response.getResponseObject());
                }
            } catch (InterruptedException e) {
                log.error("等待异常", e);
            }
        }

        return Optional.empty();
    }

    /**
     * 同步请求3.0数据项
     * @param signalControlId
     * @param objectId
     * @return
     */
    public Optional<Object> setNatsParam(String signalControlId, String objectId, List<String> datas) {

        List<JSONObject> objects = datas.stream()
                .map(JSONObject::parseObject)
                .collect(Collectors.toList());

        //构建信号机的设置数据项
        MqMessage mqMessage = OpenLesMqUtils.buildSimSetMqMsg(signalControlId, objectId, objects);

        //构建同步等待消息
        InterProtocol interProtocol = LesUtils.buildMessage("255.255.255.255", InterProtocolType.UNKOWN_MESSAGE,
                mqMessage, true, mqMessage.getAckBackKey(), 10, false, false);

        Optional<InvokeFuture> invokeFutureOp = messageOuterPublisher.sendMessageOuter(interProtocol, false);

        if(invokeFutureOp.isPresent()) {
            //处理数据项
            mqMessageProcess.mqMessageProcess(mqMessage);
            //开始等待应答
            InvokeFuture future = invokeFutureOp.get();

            try {
                LocalDateTime startTime = LocalDateTime.now();
                log.debug("开始等待-{}应答-{}", JSONObject.toJSONString(mqMessage), startTime);
                ResponseMessage response = future.waitResponse();
                LocalDateTime endTime = LocalDateTime.now();
                long millis = Duration.between(endTime, startTime).toMillis();
                log.debug("请求数据-{} \n 收到了应答-{}, \n 花费时间-{}, \n 报文中时间-{} \n" +
                                "数据应答项-{}", JSONObject.toJSONString(mqMessage)
                        , endTime, millis,
                        System.currentTimeMillis() - response.getRequestInterProtocol().getOuterTimeStamp(), response);
                /*解析应答数据项*/
                if (response.getResponseStatus() != ResponseStatus.SUCCESS) {
                    log.error("等待{}应答失败-{}", mqMessage, response.getResponseStatus().des());
                } else {
                    log.debug("请求{}应答{}花费时间{}ms", mqMessage,
                            response.getResponseObject(), millis);
                    return Optional.of(response.getResponseObject());
                }
            } catch (InterruptedException e) {
                log.error("等待异常", e);
            }
        }

        return Optional.empty();
    }


}
