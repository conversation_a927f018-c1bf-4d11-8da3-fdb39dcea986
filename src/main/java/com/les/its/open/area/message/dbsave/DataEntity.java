package com.les.its.open.area.message.dbsave;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/31 16:27
 */
@Entity
@Data
@Table(name = "DataEntity")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataEntity {
    @GenericGenerator(name = "generator", strategy = "uuid")
    @Id
    @GeneratedValue(generator = "generator")
    @Column(name = "id", length = 32, nullable = false)
    private String id;

    @Column(length = 20, nullable = false)
    private String signalId;

    /**
     * 定义的mqId
     */
    private String typeId;

    /**
     * 类型名称
     */
    private String type;

    /**
     * 查询数据no
     */
    private int no;

    /**
     * 接收数据时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    /**
     * json数据项
     */
    @Column(length = 5000000, nullable = false)
    private String data;

    /**
     * 获取内存关键字数据项
     *
     * @return
     */
    public String getKey() {
        return getDataKey(signalId, type, no);
    }

    public static String getDataKey(String signalId, String type, int no) {
        return signalId + "###" + type + "###" + no;
    }

    public static String getDataKeyPrefix(String signalId, String type) {
        return signalId + "###" + type + "###";
    }

}
