package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.SegmentParam;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.DayPlanParam;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

@Component
@Slf4j
public class DayPlanParamHandler implements MqMsgBaseHandler {


    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public DayPlanParamHandler(ControllerService controllerService, OpenLesSender openLesSender, DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }

    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_DayPlanParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class<?> dataType() {
        return DayPlanParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        //调看一个参数时
        if(datas.size() == 1) {
            Integer dayPlanNo = datas.get(0);
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_DAY_PLAN, dayPlanNo,
                    1, signalInfoOp.get());
            areaMessages.add(msg);
        }else{
            // 调看,受限于长度，每次最大只能调看8个
            final int EACH_PLAN_COUNT = ParamMsgType.PARAM_DAY_PLAN.getLookMax();
            for (int i = 0; i < ParamMsgType.PARAM_DAY_PLAN.getMax() / EACH_PLAN_COUNT; i++) {
                AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_DAY_PLAN, 1 + (EACH_PLAN_COUNT) * i,
                        EACH_PLAN_COUNT, signalInfoOp.get());
                areaMessages.add(msg);
            }
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());


        List<Integer> dataNos = new ArrayList<>();
        try {
            List<Object> objectList = requestMessage.getObjectList();
            objectList.forEach(
                    objectId -> {
                        dataNos.add((Integer) objectId);
                    }
            );
        } catch (Exception e) {
            log.error("解析参数出现异常", e);
        }

        if(dataNos.isEmpty()){
            IntStream.rangeClosed(1, ParamMsgType.PARAM_DAY_PLAN.getMax()).forEach(dataNos::add);
        }

        Optional<List<com.les.its.open.area.juncer.msg.param.lookload.dto.DayPlan>> dayPlansOp
                = OpenLesMqUtils.getDatas(itemDatas, ParamMsgType.PARAM_DAY_PLAN);
        if(dayPlansOp.isEmpty()){
            log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_DAY_PLAN);
            return Optional.empty();
        }
        log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), dayPlansOp.get());

        //构建返回数据项
        dataNos.forEach(dataNo -> {
            //根据编号查找数据项
            dayPlansOp.get().stream().filter(
                    plan -> plan.getDayPlanNo().intValue() == dataNo
            ).findAny().ifPresent( dayPlan ->

            //构建返回数据项
            {
                DayPlanParam dayPlanParam = new DayPlanParam();
                //设置信号机编号
                dayPlanParam.setSignalControllerID(requestMessage.getSignalControllerID());


                List<SegmentParam> segmentParams = new ArrayList<>();
                dayPlanParam.setSegmentParams(segmentParams);

                //日计划编号
                dayPlanParam.setDayPlanNo(dayPlan.getDayPlanNo());
                //子路口号
                dayPlanParam.setCrossingSeqNo(dayPlan.getCrossingSeqNo());

                //时段数据
                List<SegmentParam> dayPlanSegmentParams = dayPlan.getSegmentParams();
                for (int i = 0; i < dayPlanSegmentParams.size() && i < dayPlan.getSegmentNum(); i++) {
                    segmentParams.add(dayPlanSegmentParams.get(i));
                }

                //日计划名称
                dayPlanParam.setDayPlanName(dayPlan.getName());

                //应答参数
                datas.add(dayPlanParam);
            });
        });

        if(datas.isEmpty()){
            errorMsgRet.append("数据返回异常");
            return Optional.empty();
        }
        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {
        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();
        //转换格式

        List<com.les.its.open.area.juncer.msg.param.lookload.dto.DayPlan> dayPlanList
                = new ArrayList<>();


        final int EACH_PLAN_COUNT = ParamMsgType.PARAM_DAY_PLAN.getLookMax();

        for (int i = 0; i < datas.size(); i++) {

            if(i % EACH_PLAN_COUNT == 0){
                List<com.les.its.open.area.juncer.msg.param.lookload.dto.DayPlan> dayPlanListEach = new ArrayList<>();
                dayPlanListEach.addAll(dayPlanList);

                dayPlanList.clear();
                //非空数据时，每最大个数生成一个加载报文数据项
                if(!dayPlanListEach.isEmpty()){
                    //生成加载参数
                    AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_DAY_PLAN,
                            dayPlanList, signalInfoOp.get());
                    areaMessages.add(areaMessage);
                }
            }


            Object data = datas.get(i);

            JSONObject jsonObject = (JSONObject) data;
            DayPlanParam dayPlanParam = jsonObject.toJavaObject(DayPlanParam.class);
            log.error("设置参数-{}", dayPlanParam);

            //对数据进行校验
            {
                StringBuilder stringBuilder = new StringBuilder();
                boolean validateData = dataValidatorFactory.validateData(dayPlanParam, stringBuilder);
                if (!validateData) {
                    log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                            stringBuilder, dayPlanParam);
                    errorMsg.append(stringBuilder);
                    return Optional.empty();
                }
            }

            {
                com.les.its.open.area.juncer.msg.param.lookload.dto.DayPlan dayPlan = new
                        com.les.its.open.area.juncer.msg.param.lookload.dto.DayPlan();
                dayPlanList.add(dayPlan);

                //日计划编号
                dayPlan.setDayPlanNo(dayPlanParam.getDayPlanNo());
                //子路口号
                dayPlan.setCrossingSeqNo(dayPlanParam.getCrossingSeqNo());
                //时段个数
                dayPlan.setSegmentNum(dayPlanParam.getSegmentParams().size());
                dayPlanParam.getSegmentParams().stream().forEach(
                    segmentParam -> {
                        segmentParam.setActionNum(segmentParam.getSegmentActions().size());
                    }
                );
                //时段参数列表
                dayPlan.setSegmentParams(dayPlanParam.getSegmentParams());
                //名称
                dayPlan.setName(dayPlanParam.getDayPlanName());
            }
        }


        //最后的数据项
        if(!dayPlanList.isEmpty()){
            //生成加载参数
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_DAY_PLAN,
                    dayPlanList, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }


        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage,
                                                    List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return false;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}
