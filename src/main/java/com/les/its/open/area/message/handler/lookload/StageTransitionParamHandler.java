package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.StageTransitionConstraint;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.param.lookload.StageTransitionParam;
import com.les.its.open.area.message.param.lookload.sub.StageTransTable;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class StageTransitionParamHandler implements MqMsgBaseHandler {

    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public StageTransitionParamHandler(ControllerService controllerService,
                                       OpenLesSender openLesSender,
                                       DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }

    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_StageTransitionParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class<?> dataType() {
        return StageTransitionParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        // 调看过渡约束
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_STAGE_TRANS_CONSTRAINT, 1, ParamMsgType.PARAM_STAGE_TRANS_CONSTRAINT.getMax(), signalInfoOp.get());
            areaMessages.add(msg);
        }

        // 调看运行模式过渡约束
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_MODE_TRANS_CONSTRAINT, 1, ParamMsgType.PARAM_MODE_TRANS_CONSTRAINT.getMax(), signalInfoOp.get());
            areaMessages.add(msg);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        //构建返回数据项
        {
            StageTransitionParam stageTransitionParam = new StageTransitionParam();
            //设置信号机编号
            stageTransitionParam.setSignalControllerID(requestMessage.getSignalControllerID());

            List<StageTransTable> stageTransTables = new ArrayList<>();
            stageTransitionParam.setStageTransTables(stageTransTables);

            //过渡约束
            Optional<List<com.les.its.open.area.juncer.msg.param.lookload.dto.StageTransitionConstraintInfo>> startupStagesOp
                    = OpenLesMqUtils.getDatas(itemDatas, ParamMsgType.PARAM_STAGE_TRANS_CONSTRAINT);
            if(startupStagesOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_STAGE_TRANS_CONSTRAINT);
                return Optional.empty();
            }
            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), startupStagesOp.get());


            //模式过渡约束
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.ModeTransitionConstraint> modeTransitionConstraintOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_MODE_TRANS_CONSTRAINT);
            if(modeTransitionConstraintOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_MODE_TRANS_CONSTRAINT);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), modeTransitionConstraintOp.get());

            //过渡约束
            for (com.les.its.open.area.juncer.msg.param.lookload.dto.StageTransitionConstraintInfo
                    stageTransitionConstraintInfo : startupStagesOp.get()) {

                StageTransTable stageTransTable = new StageTransTable();
                stageTransTables.add(stageTransTable);

                stageTransTable.setStageTransTableNo(stageTransitionConstraintInfo.getStageTransitionConstraintNo());
                stageTransTable.setStageTransConstraint(stageTransitionConstraintInfo.getStageTransitionConstraints());

            }
            // 模式过渡约束
            {
                stageTransitionParam.setModeTrans(modeTransitionConstraintOp.get().getTransitionConstraintTableNos());
            }

            //应答参数
            datas.add(stageTransitionParam);
        }

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {
        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        //转换格式
        JSONObject jsonObject = (JSONObject) (datas.get(0));
        StageTransitionParam stageTransitionParam = jsonObject.toJavaObject(StageTransitionParam.class);

        //对数据进行校验
        {
            StringBuilder stringBuilder = new StringBuilder();
            boolean validateData = dataValidatorFactory.validateData(stageTransitionParam, stringBuilder);
            if (!validateData) {
                log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                        stringBuilder, stageTransitionParam);
                errorMsg.append(stringBuilder);
                return Optional.empty();
            }
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        //过渡约束
        {
            List<com.les.its.open.area.juncer.msg.param.lookload.dto.StageTransitionConstraintInfo> stageTransitionConstraintInfos = new ArrayList<>();
            stageTransitionParam.getStageTransTables().forEach(stageTransTable -> {
                com.les.its.open.area.juncer.msg.param.lookload.dto.StageTransitionConstraintInfo stageTransitionConstraintInfo
                        = new com.les.its.open.area.juncer.msg.param.lookload.dto.StageTransitionConstraintInfo();
                stageTransitionConstraintInfo.setStageTransitionConstraintNo(stageTransTable.getStageTransTableNo());
                stageTransitionConstraintInfo.setStageTransitionConstraints(stageTransTable.getStageTransConstraint());

                //扩展到64阶段
                int currentSize = stageTransTable.getStageTransConstraint().size();
                for (int i =  currentSize; i < 64; i++) {
                    StageTransitionConstraint stageTransitionConstraint = new StageTransitionConstraint();
                    stageTransitionConstraint.setStageNo(i + 1);
                    List<Integer> transitionConstraints = new ArrayList<>();
                    for (int j = 0; j < 64; j++) {
                        transitionConstraints.add(0);
                    }
                    stageTransitionConstraint.setTransitionConstraints(transitionConstraints);
                    stageTransTable.getStageTransConstraint().add(stageTransitionConstraint);
                }

                stageTransitionConstraintInfos.add(stageTransitionConstraintInfo);
            });

            //生成加载参数
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_STAGE_TRANS_CONSTRAINT,
                    stageTransitionConstraintInfos, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        // 模式过渡约束
        {
            com.les.its.open.area.juncer.msg.param.lookload.dto.ModeTransitionConstraint modeTransitionConstraint
                    = new com.les.its.open.area.juncer.msg.param.lookload.dto.ModeTransitionConstraint();
            modeTransitionConstraint.setTransitionConstraintTableNos(stageTransitionParam.getModeTrans());

            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_MODE_TRANS_CONSTRAINT,
                    modeTransitionConstraint, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}
