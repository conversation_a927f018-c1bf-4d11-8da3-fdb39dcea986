package com.les.its.open.area.message.dbsave;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.message.service.SignalCacheService;
import com.les.its.open.area.message.param.SgpTransAble;
import com.les.its.open.config.GlobalConfigure;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.List;

/**
 * @Author: WJ
 * @Description:
 * @Date: create in 2021/5/21 11:17
 */
@Service
@Slf4j
public class SgpDbSaveEntityProcess {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private SignalCacheService signalCacheService;

    /**
     * 根据数据类型获取请求数据的地址
     *
     * @param sgpTransAble
     * @return
     */
    private String getUrl(SgpTransAble sgpTransAble) {
        return "http://" + GlobalConfigure.signalParamIp + "/" + sgpTransAble.getUrl();
    }

    /**
     * 存储数据项
     *
     * @param data
     * @return
     */
    private boolean saveData(Object data) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/json;charset=utf-8"));
        try {
            SgpTransAble sgpTransAble = (SgpTransAble) data;
            if (sgpTransAble.getUrl() == null || sgpTransAble.getUrl().isEmpty()) {
                return false;
            }
            HttpEntity<String> httpEntity = new HttpEntity<>(JSONObject.toJSONString(sgpTransAble.transData()), headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(getUrl(sgpTransAble), httpEntity, String.class);
            if (responseEntity.getStatusCode().value() == HttpStatus.OK.value()) {
                log.debug("结果-{}", responseEntity.getBody());
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("exception", e);
        }
        return false;
    }

    @Async(GlobalConfigure.SGP_PROCESS_EXECUTOR)
    @EventListener
    public void dbSaveEntityProcess(DataSaveBean dbSaveEntity) {
        if (!GlobalConfigure.enableUseSgp) {
            return;
        }
        List<Object> objectList = dbSaveEntity.getObjectList();
        if (dbSaveEntity == null || objectList == null
                || objectList.isEmpty()) {
            return;
        }

        objectList.stream().forEach(
                object -> {
                    if (!(object instanceof SgpTransAble)) {
                        return;
                    }
                    //boolean saveData = saveData(object);
                    //log.debug("远程数据存储-{}-{}", saveData, object);
                }
        );
    }


    /**
     * 存储数据项
     *
     * @param data
     * @return
     */
    private boolean saveLocalData(Object data) {
        try {
            SgpTransAble sgpTransAble = (SgpTransAble) data;
            String jsonString = JSONObject.toJSONString(sgpTransAble);
            DataEntity dataEntity = DataEntity.builder()
                    .signalId(sgpTransAble.getSignalId())
                    .typeId(sgpTransAble.getMqNo())
                    .type(sgpTransAble.getClazzName())
                    .no(sgpTransAble.getDataNo())
                    .data(jsonString)
                    .updateDate(new Date()).build();
            signalCacheService.updateData(dataEntity);

        } catch (Exception e) {
            log.error("exception", e);
            return false;
        }
        return true;
    }

    @Async(GlobalConfigure.DB_MSG_PROCESS_EXECUTOR)
    @EventListener
    public void dbLocalSaveEntityProcess(DataSaveBean dbSaveEntity) {
        if (!GlobalConfigure.enableUseSgp) {
            return;
        }
        List<Object> objectList = dbSaveEntity.getObjectList();
        if (dbSaveEntity == null || objectList == null
                || objectList.isEmpty()) {
            return;
        }

        objectList.stream().forEach(
                object -> {
                    if (!(object instanceof SgpTransAble)) {
                        return;
                    }
                    boolean saveData = saveLocalData(object);
                    log.debug("本地数据存储-{}-{}", saveData, object);
                }
        );
    }

    /**
     * 根据数据类型获取请求数据的地址
     *
     * @param dataSavePushBean
     * @return
     */
    private String getPushUrl(DataSavePushBean dataSavePushBean) {
        return "http://" + GlobalConfigure.signalParamIp + "/" + dataSavePushBean.getRealTimeMsgInterface().getSgpUrl();
    }

    /**
     * 存储数据项
     *
     * @param data
     * @return
     */
    private boolean savePushData(DataSavePushBean data) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/json;charset=utf-8"));
        HttpEntity<String> httpEntity = new HttpEntity<>(JSONObject.toJSONString(data.getRealTimeMsgInterface().transPushData()), headers);
        try {
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(getPushUrl(data), httpEntity, String.class);
            if (responseEntity.getStatusCode().value() == HttpStatus.OK.value()) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("保存数据-{}", data);
            log.error("exception ", e);
        }
        return false;
    }

    @Async(GlobalConfigure.SGP_PROCESS_EXECUTOR)
    @EventListener
    public void dbSaveEntityProcess(DataSavePushBean dataSavePushBean) {
        if (!GlobalConfigure.enableUseSgp) {
            return;
        }
        if (dataSavePushBean == null || dataSavePushBean.getRealTimeMsgInterface() == null) {
            return;
        }

        if (dataSavePushBean.getRealTimeMsgInterface().getSgpUrl().isEmpty()) {
            return;
        }

        boolean saveData = savePushData(dataSavePushBean);
        log.debug("Push数据存储-{}-{}", saveData, dataSavePushBean);
    }


    /**
     * 存储数据项
     *
     * @param dataType
     * @param controllerId
     * @return
     */
    public boolean deleteData(String dataType, String controllerId) {
        String basicUrl = "http://" + GlobalConfigure.signalParamIp + "/" + dataType + "/" + controllerId;
        try {
            log.debug("删除数据-{}", basicUrl);
            restTemplate.delete(basicUrl);
            return true;
        } catch (Exception e) {
            log.error("删除数据-{}", basicUrl);
            log.error("exception ", e);
        }
        return false;
    }
}
