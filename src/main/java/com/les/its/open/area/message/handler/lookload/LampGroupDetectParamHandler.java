package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.Threshold;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.LampGroupDetectParam;
import com.les.its.open.area.message.param.lookload.LampGroupParam;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.param.lookload.sub.LampFaultThre;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

@Component
@Slf4j
public class LampGroupDetectParamHandler implements MqMsgBaseHandler {


    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public LampGroupDetectParamHandler(ControllerService controllerService, OpenLesSender openLesSender, DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }

    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_LampGroupDetectParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class<?> dataType() {
        return LampGroupParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        // 调看灯检测电压电流阈值配置
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_UI_THRESHOLD, signalInfoOp.get());
            areaMessages.add(msg);
        }
        // 灯检测门限配置
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_LAMP_FAULT_THRESHOLD, 1, ParamMsgType.PARAM_LAMP_FAULT_THRESHOLD.getMax(),
                    signalInfoOp.get());
            areaMessages.add(msg);
        }
        // 灯故障检测标记
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_LAMP_FAULT_DETECT_FLAG, signalInfoOp.get());
            areaMessages.add(msg);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        //构建返回数据项
        {
            LampGroupDetectParam lampGroupDetectParam = new LampGroupDetectParam();
            //设置信号机编号
            lampGroupDetectParam.setSignalControllerID(requestMessage.getSignalControllerID());

            //灯检测电压电流阈值配置
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.UIThreshold> uiThresholdsOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_UI_THRESHOLD);
            if(uiThresholdsOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_UI_THRESHOLD);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), uiThresholdsOp.get());

            //灯检测门限配置
            Optional<List<com.les.its.open.area.juncer.msg.param.lookload.dto.LampFaultThreshold>> lampFaultThresholdsOp
                    = OpenLesMqUtils.getDatas(itemDatas, ParamMsgType.PARAM_LAMP_FAULT_THRESHOLD);
            if(lampFaultThresholdsOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_LAMP_FAULT_THRESHOLD);
                return Optional.empty();
            }
            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), lampFaultThresholdsOp.get());


            //灯故障检测标记
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.LampFaultDetectFlag> lampFaultDetectFlagOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_LAMP_FAULT_DETECT_FLAG);
            if(lampFaultDetectFlagOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_LAMP_FAULT_DETECT_FLAG);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), lampFaultDetectFlagOp.get());
            {
                //灯检测电压电流阈值配置
                {
                    lampGroupDetectParam.setUiThreshold(uiThresholdsOp.get());
                }
                //灯检测阈值配置
                {
                    List<LampFaultThre> lampFaultThres = new ArrayList<>();
                    lampGroupDetectParam.setLampFaultThres(lampFaultThres);
                    lampFaultThresholdsOp.get().forEach(
                            lampFaultThreshold -> {
                                LampFaultThre lampFaultThre = new LampFaultThre();
                                lampFaultThre.setLampGroupNo(lampFaultThreshold.getLampNo());

                                //判定是否检测
                                if(lampFaultDetectFlagOp.get().getReportFlags() != null
                                        && lampFaultThreshold.getLampNo() <= lampFaultDetectFlagOp.get().getReportFlags().size()
                                        && lampFaultThreshold.getLampNo()>= 1) {
                                    lampFaultThre.setDetectFlag(
                                            lampFaultDetectFlagOp.get().getReportFlags().get(lampFaultThreshold.getLampNo() - 1));
                                }

                                lampFaultThre.setGreenThreshold(lampFaultThreshold.getGreenThreshold());
                                lampFaultThre.setYellowThreshold(lampFaultThreshold.getYellowThreshold());
                                lampFaultThre.setRedThreshold(lampFaultThreshold.getRedThreshold());
                                lampFaultThres.add(lampFaultThre);
                            }
                    );
                }
            }

            //应答参数
            datas.add(lampGroupDetectParam);
        }

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {
        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        //转换格式
        JSONObject jsonObject = (JSONObject) (datas.get(0));
        LampGroupDetectParam lampGroupDetectParam = jsonObject.toJavaObject(LampGroupDetectParam.class);

        //对数据进行校验
        {
            StringBuilder stringBuilder = new StringBuilder();
            boolean validateData = dataValidatorFactory.validateData(lampGroupDetectParam, stringBuilder);
            if (!validateData) {
                log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                        stringBuilder, lampGroupDetectParam);
                errorMsg.append(stringBuilder);
                return Optional.empty();
            }
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        //灯检测电压电流阈值配置
        {
            com.les.its.open.area.juncer.msg.param.lookload.dto.UIThreshold uiThreshold
                    = new com.les.its.open.area.juncer.msg.param.lookload.dto.UIThreshold();
            uiThreshold.setVoltageThresholdLower(lampGroupDetectParam.getUiThreshold().getVoltageThresholdLower());
            uiThreshold.setVoltageThresholdUpper(lampGroupDetectParam.getUiThreshold().getVoltageThresholdUpper());
            uiThreshold.setCurrentThresholdLower(lampGroupDetectParam.getUiThreshold().getCurrentThresholdLower());
            uiThreshold.setCurrentThresholdUpper(lampGroupDetectParam.getUiThreshold().getCurrentThresholdUpper());
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_UI_THRESHOLD, uiThreshold, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        //灯检测阈值配置
        {
            List<com.les.its.open.area.juncer.msg.param.lookload.dto.LampFaultThreshold> lampFaultThresholds
                    = new ArrayList<>();
            lampGroupDetectParam.getLampFaultThres().forEach(lampFaultThre -> {
                com.les.its.open.area.juncer.msg.param.lookload.dto.LampFaultThreshold lampFaultThreshold
                        = new com.les.its.open.area.juncer.msg.param.lookload.dto.LampFaultThreshold();
                lampFaultThreshold.setLampNo(lampFaultThre.getLampGroupNo());
                lampFaultThresholds.add(lampFaultThreshold);
                //绿灯
                {
                    Threshold greenThreshold
                            = new Threshold();
                    greenThreshold.setVoltageLower(lampFaultThre.getGreenThreshold().getVoltageLower());
                    greenThreshold.setVoltageUpper(lampFaultThre.getGreenThreshold().getVoltageUpper());
                    greenThreshold.setCurrentLower(lampFaultThre.getGreenThreshold().getCurrentLower());
                    greenThreshold.setCurrentUpper(lampFaultThre.getGreenThreshold().getCurrentUpper());
                    lampFaultThreshold.setGreenThreshold(greenThreshold);
                }
                //黄灯
                {
                    Threshold yellowThreshold
                            = new Threshold();
                    yellowThreshold.setVoltageLower(lampFaultThre.getYellowThreshold().getVoltageLower());
                    yellowThreshold.setVoltageUpper(lampFaultThre.getYellowThreshold().getVoltageUpper());
                    yellowThreshold.setCurrentLower(lampFaultThre.getYellowThreshold().getCurrentLower());
                    yellowThreshold.setCurrentUpper(lampFaultThre.getYellowThreshold().getCurrentUpper());
                    lampFaultThreshold.setYellowThreshold(yellowThreshold);
                }
                //红灯
                {
                    Threshold redThreshold
                            = new Threshold();
                    redThreshold.setVoltageLower(lampFaultThre.getRedThreshold().getVoltageLower());
                    redThreshold.setVoltageUpper(lampFaultThre.getRedThreshold().getVoltageUpper());
                    redThreshold.setCurrentLower(lampFaultThre.getRedThreshold().getCurrentLower());
                    redThreshold.setCurrentUpper(lampFaultThre.getRedThreshold().getCurrentUpper());
                    lampFaultThreshold.setRedThreshold(redThreshold);
                }
            });
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_LAMP_FAULT_THRESHOLD, lampFaultThresholds, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        //灯故障检测标记
        {
            com.les.its.open.area.juncer.msg.param.lookload.dto.LampFaultDetectFlag lampFaultDetectFlag
                    = new com.les.its.open.area.juncer.msg.param.lookload.dto.LampFaultDetectFlag();
            List<Integer> reportFlags = new ArrayList<>();
            lampFaultDetectFlag.setReportFlags(reportFlags);

            IntStream.rangeClosed(1, 64).forEach(
                    lampNo -> {
                        Optional<LampFaultThre> faultThreOp= lampGroupDetectParam.getLampFaultThres().stream().filter(lampFaultThre ->
                                lampFaultThre.getLampGroupNo().intValue() == lampNo).findAny();
                        if(faultThreOp.isPresent())  {
                            reportFlags.add(faultThreOp.get().getDetectFlag());
                        }else{
                            reportFlags.add(0);
                        }
                    }
            );

            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_LAMP_FAULT_DETECT_FLAG, lampFaultDetectFlag, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}
