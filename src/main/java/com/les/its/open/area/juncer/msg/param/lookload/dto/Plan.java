package com.les.its.open.area.juncer.msg.param.lookload.dto;

import com.les.its.open.bussiness.bean.DataIndexAble;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Plan implements DataIndexAble {
    // 方案编号
    @NotNull(message = "方案编号不能为空")
    @Range(min = 1, max = 128, message = "方案编号有效范围为[1,128]")
    private Integer planNo;
    
    // 子路口号
    @NotNull(message = "子路口号不能为空")
    @Range(min = 1, max = 8, message = "子路口号有效范围为[1,8]")
    private Integer crossingSeqNo;

    // 周期
    @NotNull(message = "周期不能为空")
    @Range(min = 1, max = 65535, message = "周期有效范围为[1,65535]")
    private Integer cycle;
    
    // 协调序号
    @NotNull(message = "协调序号不能为空")
    @Range(min = 1, max = 16, message = "协调序号有效范围为[1,16]")
    private Integer coordinatedStageSeq;
    
    // 协调参考点 (绿灯初，黄灯初，红灯初)
    @NotNull(message = "协调参考点不能为空")
    @Range(min = 0, max = 2, message = "协调参考点有效范围为[0,2]")
    private Integer coordinatedRef;
    
    // 相位差
    @NotNull(message = "相位差不能为空")
    @Range(min =0, max = 65535, message = "相位差有效范围为[0,65535]")
    private Integer offset;
    
    // 阶段个数
    @NotNull(message = "阶段个数不能为空")
    @Range(min = 1, max = 16, message = "阶段个数有效范围为[1,16]")
    private Integer stageNumber;
    
    // 阶段信息 [阶段序列也要检查阶段过渡规约]
    @NotNull(message = "阶段信息不能为空")
    @Size(min = 16, max = 16, message = "阶段信息数量必须为16")
    @Valid
    private List<PlanStageParam> planStageParams;

    // 方案名称
    @NotNull(message = "方案名称不能为空")
    private String planName;

    @Override
    public int getDataNo() {
        return planNo;
    }

    /**
     * Custom toString for concise logging output
     * Only shows key identifiers and summary information
     */
    @Override
    public String toString() {
        return String.format("Plan{planNo=%d, crossingSeqNo=%d, planName='%s', cycle=%d, " +
                "coordinatedStageSeq=%d, offset=%d, stageNumber=%d, stageCount=%d}",
                planNo, crossingSeqNo, planName, cycle, coordinatedStageSeq, offset, 
                stageNumber, planStageParams != null ? planStageParams.size() : 0);
    }

    // Getter and Setter methods
    public Integer getPlanNo() { return planNo; }
    public void setPlanNo(Integer planNo) { this.planNo = planNo; }
    
    public Integer getCrossingSeqNo() { return crossingSeqNo; }
    public void setCrossingSeqNo(Integer crossingSeqNo) { this.crossingSeqNo = crossingSeqNo; }
    
    public Integer getCycle() { return cycle; }
    public void setCycle(Integer cycle) { this.cycle = cycle; }
    
    public Integer getCoordinatedStageSeq() { return coordinatedStageSeq; }
    public void setCoordinatedStageSeq(Integer coordinatedStageSeq) { this.coordinatedStageSeq = coordinatedStageSeq; }
    
    public Integer getCoordinatedRef() { return coordinatedRef; }
    public void setCoordinatedRef(Integer coordinatedRef) { this.coordinatedRef = coordinatedRef; }
    
    public Integer getOffset() { return offset; }
    public void setOffset(Integer offset) { this.offset = offset; }
    
    public Integer getStageNumber() { return stageNumber; }
    public void setStageNumber(Integer stageNumber) { this.stageNumber = stageNumber; }
    
    public List<PlanStageParam> getPlanStageParams() { return planStageParams; }
    public void setPlanStageParams(List<PlanStageParam> planStageParams) { this.planStageParams = planStageParams; }
    
    public String getPlanName() { return planName; }
    public void setPlanName(String planName) { this.planName = planName; }
    
    // Equals and HashCode methods
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Plan plan = (Plan) o;
        return java.util.Objects.equals(planNo, plan.planNo) &&
               java.util.Objects.equals(crossingSeqNo, plan.crossingSeqNo) &&
               java.util.Objects.equals(cycle, plan.cycle) &&
               java.util.Objects.equals(coordinatedStageSeq, plan.coordinatedStageSeq) &&
               java.util.Objects.equals(coordinatedRef, plan.coordinatedRef) &&
               java.util.Objects.equals(offset, plan.offset) &&
               java.util.Objects.equals(stageNumber, plan.stageNumber) &&
               java.util.Objects.equals(planStageParams, plan.planStageParams) &&
               java.util.Objects.equals(planName, plan.planName);
    }
    
    @Override
    public int hashCode() {
        return java.util.Objects.hash(planNo, crossingSeqNo, cycle, coordinatedStageSeq, 
                                    coordinatedRef, offset, stageNumber, planStageParams, planName);
    }
}