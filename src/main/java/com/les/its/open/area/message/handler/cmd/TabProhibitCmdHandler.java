package com.les.its.open.area.message.handler.cmd;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.cmd.TabProhibitCmd;
import com.les.its.open.area.juncer.msg.cmd.TabProhibitCmdAck;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.cmd.CmdMqObject;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.MsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class TabProhibitCmdHandler implements MqMsgBaseHandler {

    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public TabProhibitCmdHandler(ControllerService controllerService,
                                 OpenLesSender openLesSender,
                                 DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }

    @Override
    public String getObjectId() {
        return CmdMqObject.TAB_PROHIBIT_CMD.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class<?> dataType() {
        return TabProhibitCmd.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        return Optional.empty();
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures,
                                              StringBuilder errorMsgRet) {
        return Optional.empty();
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return false;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {
        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        //转换格式
        JSONObject jsonObject = (JSONObject) (datas.get(0));
        TabProhibitCmd tabProhibitCmd = jsonObject.toJavaObject(TabProhibitCmd.class);

        //对数据进行校验
        StringBuilder stringBuilder = new StringBuilder();
        boolean validateData = dataValidatorFactory.validateData(tabProhibitCmd, stringBuilder);
        if (!validateData) {
            log.error("信号机{}控制命令校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                    stringBuilder, tabProhibitCmd);
            errorMsg.append(stringBuilder);
            return Optional.empty();
        }


        List<AreaMessage> areaMessages = new ArrayList<>();
        //生成加载参数
        AreaMessage areaMessage = AreaMessage.genCmdMsg(MsgType.TAB_PROHIBIT_CMD, tabProhibitCmd, signalInfoOp.get());
        areaMessages.add(areaMessage);

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures,
                                                    StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());
        if (itemDatas == null || itemDatas.isEmpty()) {
            return Optional.empty();
        }

        JsonResult<?> itemData = itemDatas.get(0);
        AtomicInteger ackErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        log.debug("信号机{}控制应答{}", requestMessage.getSignalControllerID(), itemData);
        if (itemData.getData() instanceof TabProhibitCmdAck taAck && taAck.getAck() != 0) {
                log.error("信号机{}控制应答异常{}", requestMessage.getSignalControllerID(), itemData);
                ackErrorCount.incrementAndGet();
                errorMsg.append(String.format("[%s]控制异常[%d];", taAck.getMsgType().getDescription(),
                        taAck.getReason()));
        }

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查控制是否错误
        if(ackErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return false;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}
