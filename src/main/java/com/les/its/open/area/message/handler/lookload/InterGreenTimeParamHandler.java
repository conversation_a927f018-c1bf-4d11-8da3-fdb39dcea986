package com.les.its.open.area.message.handler.lookload;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.param.TabLoad;
import com.les.its.open.area.juncer.msg.param.lookload.dto.InterGreenTime;
import com.les.its.open.area.juncer.msg.param.lookload.dto.InterGreenTimeTable;
import com.les.its.open.area.juncer.msg.param.lookload.dto.valid.DataValidatorFactory;
import com.les.its.open.area.message.OpenLesSender;
import com.les.its.open.area.message.handler.MqMsgBaseHandler;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.mq.MqMessage;
import com.les.its.open.area.message.param.lookload.InterGreenTimeParam;
import com.les.its.open.area.message.param.lookload.LookLoadMqObject;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.message.utils.OpenLesMqUtils;
import com.les.its.open.area.net.msg.ParamMsgType;
import com.les.its.open.event.AckManager.InvokeFuture;
import com.myweb.commons.persistence.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;

@Component
@Slf4j
public class InterGreenTimeParamHandler implements MqMsgBaseHandler {

    private final ControllerService controllerService;

    private final OpenLesSender openLesSender;

    private final DataValidatorFactory dataValidatorFactory;

    public InterGreenTimeParamHandler(ControllerService controllerService,
                                      OpenLesSender openLesSender,
                                      DataValidatorFactory dataValidatorFactory) {
        this.controllerService = controllerService;
        this.openLesSender = openLesSender;
        this.dataValidatorFactory = dataValidatorFactory;
    }

    @Override
    public String getObjectId() {
        return LookLoadMqObject.LookLoad_InterGreenTimeParam.objectId();
    }

    @Override
    public String getRoutingKey() {
        return dataType().getSimpleName().toLowerCase();
    }

    @Override
    public Class<?> dataType() {
        return InterGreenTimeParam.class;
    }

    @Override
    public Optional<List<AreaMessage>> getRequestMsg(String controllerId, List<Integer> datas) {
        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            return Optional.empty();
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        // 调看相位绿间隔配置
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_INTER_GREEN_TIME, 1, ParamMsgType.PARAM_INTER_GREEN_TIME.getMax(), signalInfoOp.get());
            areaMessages.add(msg);
        }

        // 调看运行模式绿间隔
        {
            AreaMessage msg = AreaMessage.genLookMsg(ParamMsgType.PARAM_MODE_INTER_GREEN, 1, ParamMsgType.PARAM_MODE_INTER_GREEN.getMax(), signalInfoOp.get());
            areaMessages.add(msg);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if(!result.isSuccess()){
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }

        List<Object> datas = new ArrayList<>();
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        //构建返回数据项
        {
            InterGreenTimeParam interGreenTimeParam = new InterGreenTimeParam();
            //设置信号机编号
            interGreenTimeParam.setSignalControllerID(requestMessage.getSignalControllerID());

            List<InterGreenTimeTable> interGreenTimeTables = new ArrayList<>();
            interGreenTimeParam.setInterGreenTimeTables(interGreenTimeTables);

            List<Integer> modeTrans = new ArrayList<>();
            interGreenTimeParam.setModeTrans(modeTrans);

            //相位绿间隔配置
            Optional<List<com.les.its.open.area.juncer.msg.param.lookload.dto.InterGreenTimeTable>> interGreenTimeTableOp
                    = OpenLesMqUtils.getDatas(itemDatas, ParamMsgType.PARAM_INTER_GREEN_TIME);
            if(interGreenTimeTableOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_INTER_GREEN_TIME);
                return Optional.empty();
            }
            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), interGreenTimeTableOp.get());


            //模式绿间隔
            Optional<com.les.its.open.area.juncer.msg.param.lookload.dto.ModeInterGreenTime> modeInterGreenTimeOp
                    = OpenLesMqUtils.getOneData(itemDatas, ParamMsgType.PARAM_MODE_INTER_GREEN);
            if(modeInterGreenTimeOp.isEmpty()){
                log.error("参数信号机{}调看失败-{}", requestMessage.getSignalControllerID(), ParamMsgType.PARAM_MODE_INTER_GREEN);
                return Optional.empty();
            }

            log.error("调看返回信号机{}-参数-{}", requestMessage.getSignalControllerID(), modeInterGreenTimeOp.get());

            //相位绿间隔配置
            interGreenTimeTables.addAll(interGreenTimeTableOp.get());

            //模式绿间隔
            modeTrans.addAll(modeInterGreenTimeOp.get().getModeInterGreenTimes());

            //应答参数
            datas.add(interGreenTimeParam);
        }

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, datas);
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean requestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<List<AreaMessage>> getConfigRequestMsg(String controllerId, List<Object> datas, StringBuilder errorMsg) {
        if (datas == null || datas.isEmpty()) {
            errorMsg.append("设置参数为空");
            return Optional.empty();
        }

        Optional<ControllerBaseInfo> signalInfoOp = controllerService.getSignalInfo(controllerId);
        if (signalInfoOp.isEmpty()) {
            errorMsg.append("信号机不存在");
            return Optional.empty();
        }

        //转换格式
        JSONObject jsonObject = (JSONObject) (datas.get(0));
        InterGreenTimeParam interGreenTimeParam = jsonObject.toJavaObject(InterGreenTimeParam.class);

        //对数据进行校验
        {
            StringBuilder stringBuilder = new StringBuilder();
            boolean validateData = dataValidatorFactory.validateData(interGreenTimeParam, stringBuilder);
            if (!validateData) {
                log.error("信号机{}参数校验失败-失败项-[{}]-数据项-[{}]", signalInfoOp.get().getSignalId(),
                        stringBuilder, interGreenTimeParam);
                errorMsg.append(stringBuilder);
                return Optional.empty();
            }
        }

        List<AreaMessage> areaMessages = new ArrayList<>();

        // 绿间隔配置
        {
            List<com.les.its.open.area.juncer.msg.param.lookload.dto.InterGreenTimeTable>
                    interGreenTimeTableList = new ArrayList<>();
            interGreenTimeTableList.addAll(interGreenTimeParam.getInterGreenTimeTables());
            
            //扩展数据
            interGreenTimeParam.getInterGreenTimeTables().stream().forEach(
                    interGreenTimeTable -> {
                        List<InterGreenTime> interGreenTimes = interGreenTimeTable.getInterGreenTimes();
                        
                        interGreenTimes.forEach(
                                interGreenTime -> {
                                    //扩展到64相位
                                    for (int i = interGreenTime.getInterGreenTimeSeq().size(); i < 64; i++) {
                                        interGreenTime.getInterGreenTimeSeq().add(0);
                                    }
                                }
                        );
                        
                        //扩展到64相位
                        int currentSize = interGreenTimes.size();
                        for (int i = currentSize; i < 64; i++) {
                            InterGreenTime interGreenTime = new InterGreenTime();
                            interGreenTime.setPhaseNo(i + 1);

                            List<Integer> interGreenTimeSeq = new ArrayList<>();
                            IntStream.rangeClosed(1,64).forEach(
                                index -> {
                                    interGreenTimeSeq.add(0);
                                }
                            );
                            interGreenTime.setInterGreenTimeSeq(interGreenTimeSeq);
                            interGreenTimes.add(interGreenTime);
                        }
                    }
            );

            //生成加载参数
            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_INTER_GREEN_TIME,
                    interGreenTimeTableList, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        // 模式绿间隔
        {
            com.les.its.open.area.juncer.msg.param.lookload.dto.ModeInterGreenTime modeInterGreenTime
                    = new com.les.its.open.area.juncer.msg.param.lookload.dto.ModeInterGreenTime();
            modeInterGreenTime.setModeInterGreenTimes(interGreenTimeParam.getModeTrans());

            AreaMessage areaMessage = AreaMessage.genLoadMsg(ParamMsgType.PARAM_MODE_INTER_GREEN,
                    modeInterGreenTime, signalInfoOp.get());
            areaMessages.add(areaMessage);
        }

        return Optional.of(areaMessages);
    }

    @Override
    public Optional<MqMessage> getConfigResponseMsg(MqMessage requestMessage, List<InvokeFuture> invokeFutures, StringBuilder errorMsgRet) {
        JsonResult<?> result = openLesSender.analyzeData(invokeFutures);
        if (!result.isSuccess()) {
            errorMsgRet.append(result.getMessage());
            return Optional.empty();
        }
        List<JsonResult<?>> itemDatas = (List<JsonResult<?>>) (result.getData());

        AtomicInteger resultErrorCount = new AtomicInteger(0);
        StringBuilder errorMsg = new StringBuilder();
        itemDatas.forEach(itemData -> {
            log.debug("信号机{}加载应答{}", requestMessage.getSignalControllerID(), itemData);
            if(itemData.getData() instanceof TabLoad tabLoad){
                if(tabLoad.getErrorCode() != 0){
                    log.error("信号机{}加载应答异常{}", requestMessage.getSignalControllerID(), itemData);
                    resultErrorCount.incrementAndGet();
                    errorMsg.append(String.format("[%s]加载异常[%d];", tabLoad.getParamMsgType().getDescription(), tabLoad.getErrorCode()));
                }
            }
        });

        //构建数据项
        MqMessage mqMessageResponse = OpenLesMqUtils.buildSuccessMqResponseMsg(requestMessage, requestMessage.getObjectList());
        //检查加载是否错误
        if(resultErrorCount.intValue() > 0){
            mqMessageResponse = OpenLesMqUtils.buildErrorMqResponseMsg(requestMessage, "9006", errorMsg.toString());
        }
        return Optional.of(mqMessageResponse);
    }

    @Override
    public boolean configRequestMsgNeedSerial(String controllerId) {
        return true;
    }

    @Override
    public Optional<MqMessage> ackMqMessage(MqMessage requestMessage, int ack) {
        return Optional.empty();
    }
}
