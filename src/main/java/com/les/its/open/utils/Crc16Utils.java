package com.les.its.open.utils;

import cn.hutool.core.io.checksum.CRC16;
import com.les.its.open.protocol.openles.message.OpenLesMessage;

public class Crc16Utils {


    /**
     * 计算CRC值数据项
     * @param openLesMessage
     * @return
     */
    public static int calculate(OpenLesMessage openLesMessage) {
        byte[] data = openLesMessage.genSendBodyWithOutParity();
        CRC16 crc16 = new CRC16();
        crc16.update(data, 0, data.length);
        return (int)(crc16.getValue() & 0xffff);
    }

    public static int calculate(byte[] data, int src, int length) {

        if(length < 0){
            throw new  IndexOutOfBoundsException();
        }

        CRC16 crc16 = new CRC16();
        crc16.update(data, src, length);
        return (int)(crc16.getValue() & 0xffff);
    }

    public static int calculate(byte[] data) {
       return calculate(data, 0, data.length);
    }

    // 使用示例
    public static void main(String[] args) {
        String text = "Hello World";
        byte[] bytes = text.getBytes();
        int crc = calculate(bytes);
        System.out.printf("CRC-16-IBM: 0x%04X%n", crc);
    }
}
