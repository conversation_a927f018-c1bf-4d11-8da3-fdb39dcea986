package com.les.its.open.area.message.service;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.message.dbsave.DataEntity;
import com.les.its.open.area.message.dbsave.QDataEntity;
import com.myweb.commons.dao.RepositoryDao;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPADeleteClause;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("SignalCacheService Tests")
class SignalCacheServiceTest {

    @Mock
    private JPAQueryFactory queryFactory;

    @Mock
    private RepositoryDao repositoryDao;

    @Mock
    private JPADeleteClause jpaDeleteClause;

    @InjectMocks
    private SignalCacheService signalCacheService;

    private static final String TEST_SIGNAL_ID = "TEST_SIGNAL_001";
    private static final String TEST_TYPE = "TestType";
    private static final int TEST_NO = 1;
    private static final String TEST_DATA = "{\"id\":1,\"name\":\"test\"}";

    private DataEntity createTestDataEntity() {
        DataEntity dataEntity = new DataEntity();
        dataEntity.setSignalId(TEST_SIGNAL_ID);
        dataEntity.setType(TEST_TYPE);
        dataEntity.setNo(TEST_NO);
        dataEntity.setData(TEST_DATA);
        dataEntity.setTypeId("100");
        return dataEntity;
    }

    private static class TestDataClass {
        private int id;
        private String name;
        
        // Getters and setters
        public int getId() { return id; }
        public void setId(int id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }

    @BeforeEach
    void setUp() {
        // Clear the cache before each test
        signalCacheService.getSignalInfoMap().clear();
    }

    @Test
    @DisplayName("loadDataFromDb should load data from database and populate cache")
    void loadDataFromDb_ShouldLoadDataFromDatabaseAndPopulateCache() {
        // Arrange
        List<DataEntity> mockDataEntities = Arrays.asList(
                createTestDataEntity(),
                createDataEntity("SIGNAL_002", "Type2", 2),
                createDataEntity("SIGNAL_003", "Type3", 3)
        );

        com.querydsl.jpa.impl.JPAQuery mockQuery = mock(com.querydsl.jpa.impl.JPAQuery.class);
        when(queryFactory.selectFrom(QDataEntity.dataEntity)).thenReturn(mockQuery);
        when(mockQuery.fetch()).thenReturn(mockDataEntities);

        // Act
        signalCacheService.loadDataFromDb();

        // Assert
        assertThat(signalCacheService.getSignalInfoMap()).isNotEmpty();
        assertThat(signalCacheService.getSignalInfoMap()).hasSize(3);
    }

    @Test
    @DisplayName("loadDataFromDb should handle empty database gracefully")
    void loadDataFromDb_WhenDatabaseEmpty_ShouldHandleGracefully() {
        // Arrange
        com.querydsl.jpa.impl.JPAQuery mockQuery = mock(com.querydsl.jpa.impl.JPAQuery.class);
        when(queryFactory.selectFrom(QDataEntity.dataEntity)).thenReturn(mockQuery);
        when(mockQuery.fetch()).thenReturn(Collections.emptyList());


        // Act
        signalCacheService.loadDataFromDb();

        // Assert
        assertThat(signalCacheService.getSignalInfoMap()).isEmpty();
        verify(queryFactory).selectFrom(QDataEntity.dataEntity);
    }

    @Test
    @DisplayName("loadDataFromDb should handle database exception gracefully")
    void loadDataFromDb_WhenDatabaseThrowsException_ShouldHandleGracefully() {
        // Arrange
        when(queryFactory.selectFrom(QDataEntity.dataEntity)).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        assertThatCode(() -> signalCacheService.loadDataFromDb())
                .doesNotThrowAnyException();

        assertThat(signalCacheService.getSignalInfoMap()).isEmpty();
    }

    @Test
    @DisplayName("deleteDataEntity should remove data from cache and database by type")
    void deleteDataEntity_WhenCalledWithType_ShouldRemoveDataFromCacheAndDatabase() {
        // Arrange
        DataEntity dataEntity = createTestDataEntity();
        ConcurrentHashMap<String, DataEntity> signalMap = new ConcurrentHashMap<>();
        signalMap.put(dataEntity.getKey(), dataEntity);
        signalCacheService.getSignalInfoMap().put(TEST_SIGNAL_ID, signalMap);

        when(queryFactory.delete(QDataEntity.dataEntity)).thenReturn(jpaDeleteClause);
        when(jpaDeleteClause.where(any())).thenReturn(jpaDeleteClause);
        when(jpaDeleteClause.execute()).thenReturn(1L);

        // Act
        signalCacheService.deleteDataEntity(TEST_SIGNAL_ID, TEST_TYPE);

        // Assert
        assertThat(signalCacheService.getSignalInfoMap().get(TEST_SIGNAL_ID)).isEmpty();
        verify(queryFactory).delete(QDataEntity.dataEntity);
        verify(jpaDeleteClause).execute();
    }

    @Test
    @DisplayName("deleteDataEntity should remove specific data from cache and database by type and no")
    void deleteDataEntity_WhenCalledWithTypeAndNo_ShouldRemoveSpecificDataFromCacheAndDatabase() {
        // Arrange
        DataEntity dataEntity = createTestDataEntity();
        ConcurrentHashMap<String, DataEntity> signalMap = new ConcurrentHashMap<>();
        signalMap.put(dataEntity.getKey(), dataEntity);
        signalCacheService.getSignalInfoMap().put(TEST_SIGNAL_ID, signalMap);

        when(queryFactory.delete(QDataEntity.dataEntity)).thenReturn(jpaDeleteClause);
        when(jpaDeleteClause.where(any())).thenReturn(jpaDeleteClause);
        when(jpaDeleteClause.execute()).thenReturn(1L);

        // Act
        signalCacheService.deleteDataEntity(TEST_SIGNAL_ID, TEST_TYPE, TEST_NO);

        // Assert
        assertThat(signalCacheService.getSignalInfoMap().get(TEST_SIGNAL_ID)).isEmpty();
        verify(queryFactory).delete(QDataEntity.dataEntity);
        verify(jpaDeleteClause).execute();
    }

    @Test
    @DisplayName("deleteDataEntity should handle non-existent signal ID gracefully")
    void deleteDataEntity_WhenSignalIdNotExists_ShouldHandleGracefully() {
        // Arrange
        when(queryFactory.delete(QDataEntity.dataEntity)).thenReturn(jpaDeleteClause);
        when(jpaDeleteClause.where(any())).thenReturn(jpaDeleteClause);
        when(jpaDeleteClause.execute()).thenReturn(0L);

        // Act & Assert
        assertThatCode(() -> signalCacheService.deleteDataEntity("NON_EXISTENT", TEST_TYPE))
                .doesNotThrowAnyException();

        verify(queryFactory).delete(QDataEntity.dataEntity);
        verify(jpaDeleteClause).execute();
    }

    @Test
    @DisplayName("updateData should update cache and database")
    void updateData_ShouldUpdateCacheAndDatabase() {
        // Arrange
        DataEntity dataEntity = createTestDataEntity();
        
        when(queryFactory.delete(QDataEntity.dataEntity)).thenReturn(jpaDeleteClause);
        when(jpaDeleteClause.where(any())).thenReturn(jpaDeleteClause);
        when(jpaDeleteClause.execute()).thenReturn(1L);

        // Act
        signalCacheService.updateData(dataEntity);

        // Assert
        Map<String, DataEntity> signalMap = signalCacheService.getSignalInfoMap().get(TEST_SIGNAL_ID);
        assertThat(signalMap).isNotNull();
        assertThat(signalMap).containsKey(dataEntity.getKey());
        assertThat(signalMap.get(dataEntity.getKey())).isEqualTo(dataEntity);
        
        verify(queryFactory).delete(QDataEntity.dataEntity);
        verify(repositoryDao).save(dataEntity, DataEntity.class);
    }

    @Test
    @DisplayName("updateData should create new signal map if not exists")
    void updateData_WhenSignalMapNotExists_ShouldCreateNewSignalMap() {
        // Arrange
        DataEntity dataEntity = createTestDataEntity();
        
        when(queryFactory.delete(QDataEntity.dataEntity)).thenReturn(jpaDeleteClause);
        when(jpaDeleteClause.where(any())).thenReturn(jpaDeleteClause);
        when(jpaDeleteClause.execute()).thenReturn(0L);

        // Act
        signalCacheService.updateData(dataEntity);

        // Assert
        assertThat(signalCacheService.getSignalInfoMap()).containsKey(TEST_SIGNAL_ID);
        Map<String, DataEntity> signalMap = signalCacheService.getSignalInfoMap().get(TEST_SIGNAL_ID);
        assertThat(signalMap).containsKey(dataEntity.getKey());
    }

    @Test
    @DisplayName("getData should return data when exists in cache")
    void getData_WhenDataExistsInCache_ShouldReturnData() {
        // Arrange
        DataEntity dataEntity = createTestDataEntity();
        dataEntity.setType(TestDataClass.class.getSimpleName());
        ConcurrentHashMap<String, DataEntity> signalMap = new ConcurrentHashMap<>();
        signalMap.put(dataEntity.getKey(), dataEntity);
        signalCacheService.getSignalInfoMap().put(TEST_SIGNAL_ID, signalMap);

        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            TestDataClass expectedData = new TestDataClass();
            expectedData.setId(1);
            expectedData.setName("test");

            // Act
            Optional<TestDataClass> result = signalCacheService.getData(TEST_SIGNAL_ID, TEST_NO, TestDataClass.class);

            // Assert
            assertThat(result).isPresent();
            assertThat(result.get().getId()).isEqualTo(1);
            assertThat(result.get().getName()).isEqualTo("test");
        }
    }

    @Test
    @DisplayName("getData should return empty when signal ID not found")
    void getData_WhenSignalIdNotFound_ShouldReturnEmpty() {
        // Act
        Optional<TestDataClass> result = signalCacheService.getData("NON_EXISTENT", TEST_NO, TestDataClass.class);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("getData should return empty when data key not found")
    void getData_WhenDataKeyNotFound_ShouldReturnEmpty() {
        // Arrange
        ConcurrentHashMap<String, DataEntity> signalMap = new ConcurrentHashMap<>();
        signalCacheService.getSignalInfoMap().put(TEST_SIGNAL_ID, signalMap);

        // Act
        Optional<TestDataClass> result = signalCacheService.getData(TEST_SIGNAL_ID, 999, TestDataClass.class);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("getDatas should return list of data when exists in cache")
    void getDatas_WhenDataExistsInCache_ShouldReturnList() {
        // Arrange
        DataEntity dataEntity1 = createDataEntity(TEST_SIGNAL_ID, "TestDataClass", 1);
        DataEntity dataEntity2 = createDataEntity(TEST_SIGNAL_ID, "TestDataClass", 2);
        
        ConcurrentHashMap<String, DataEntity> signalMap = new ConcurrentHashMap<>();
        signalMap.put(dataEntity1.getKey(), dataEntity1);
        signalMap.put(dataEntity2.getKey(), dataEntity2);
        signalCacheService.getSignalInfoMap().put(TEST_SIGNAL_ID, signalMap);

        String testData1 = "{\"id\":1,\"name\":\"test1\"}";
        String testData2 = "{\"id\":2,\"name\":\"test2\"}";
        dataEntity1.setData(testData1);
        dataEntity2.setData(testData2);

        // 使用更简单的mock方式
        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {

            // Act
            Optional<List<TestDataClass>> result = signalCacheService.getDatas(TEST_SIGNAL_ID, TestDataClass.class);

            // Assert
            assertThat(result).isPresent();
            assertThat(result.get()).hasSize(2);
        }
    }

    @Test
    @DisplayName("getDatas should return empty when signal ID not found")
    void getDatas_WhenSignalIdNotFound_ShouldReturnEmpty() {
        // Act
        Optional<List<TestDataClass>> result = signalCacheService.getDatas("NON_EXISTENT", TestDataClass.class);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("getDatas should return empty when no matching data found")
    void getDatas_WhenNoMatchingDataFound_ShouldReturnEmpty() {
        // Arrange
        ConcurrentHashMap<String, DataEntity> signalMap = new ConcurrentHashMap<>();
        DataEntity differentTypeEntity = createDataEntity(TEST_SIGNAL_ID, "DifferentType", 1);
        signalMap.put(differentTypeEntity.getKey(), differentTypeEntity);
        signalCacheService.getSignalInfoMap().put(TEST_SIGNAL_ID, signalMap);

        // Act
        Optional<List<TestDataClass>> result = signalCacheService.getDatas(TEST_SIGNAL_ID, TestDataClass.class);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("Class should have correct Spring annotations")
    void class_ShouldHaveCorrectSpringAnnotations() {
        // Assert
        assertThat(SignalCacheService.class.isAnnotationPresent(Service.class)).isTrue();
    }

    @Test
    @DisplayName("loadDataFromDb should handle large dataset efficiently")
    void loadDataFromDb_WhenLargeDataset_ShouldHandleEfficiently() {
        // Arrange
        List<DataEntity> largeDataset = new ArrayList<>();
        for (int i = 0; i < 10000; i++) {
            largeDataset.add(createDataEntity("SIGNAL_" + i, "Type", 1));
        }

        when(queryFactory.selectFrom(QDataEntity.dataEntity)).thenReturn(
                mock(com.querydsl.jpa.impl.JPAQuery.class)
        );
        when(queryFactory.selectFrom(QDataEntity.dataEntity).fetch()).thenReturn(largeDataset);

        // Act
        long startTime = System.currentTimeMillis();
        signalCacheService.loadDataFromDb();
        long endTime = System.currentTimeMillis();

        // Assert
        assertThat(signalCacheService.getSignalInfoMap()).hasSize(10000);
        assertThat(endTime - startTime).isLessThan(5000); // Should complete within 5 seconds
    }

    @Test
    @DisplayName("updateData should handle null signal ID gracefully")
    void updateData_WhenSignalIdIsNull_ShouldHandleGracefully() {
        // Arrange
        DataEntity dataEntity = createTestDataEntity();
        dataEntity.setSignalId(null);

        // Act & Assert
        assertThatCode(() -> signalCacheService.updateData(dataEntity))
                .doesNotThrowAnyException();
    }

    @Test
    @DisplayName("getData should handle JSON parsing exception gracefully")
    void getData_WhenJsonParsingFails_ShouldThrowException() {
        // Arrange
        DataEntity dataEntity = createTestDataEntity();
        dataEntity.setData("invalid json");
        dataEntity.setType(TestDataClass.class.getSimpleName());
        ConcurrentHashMap<String, DataEntity> signalMap = new ConcurrentHashMap<>();
        signalMap.put(dataEntity.getKey(), dataEntity);
        signalCacheService.getSignalInfoMap().put(TEST_SIGNAL_ID, signalMap);

        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {

            // Act & Assert
            assertThatThrownBy(() -> 
                signalCacheService.getData(TEST_SIGNAL_ID, TEST_NO, TestDataClass.class)
            ).isInstanceOf(RuntimeException.class);
        }
    }

    private DataEntity createDataEntity(String signalId, String type, int no) {
        DataEntity dataEntity = new DataEntity();
        dataEntity.setSignalId(signalId);
        dataEntity.setType(type);
        dataEntity.setNo(no);
        dataEntity.setData(TEST_DATA);
        dataEntity.setTypeId("100");
        return dataEntity;
    }
}