package com.les.its.open.area.message;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.message.mq.AreaMessage;
import com.les.its.open.area.message.service.ControllerService;
import com.les.its.open.area.message.service.dto.ControllerBaseInfo;
import com.les.its.open.area.net.proc.basic.InMsgService;
import com.les.its.open.config.GlobalConfigure;
import com.les.its.open.event.MessagePublisher;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("AreaMessageProcess Tests")
class AreaMessageProcessTest {

    @Mock
    private MessagePublisher messagePublisher;

    @Mock
    private ControllerService controllerService;

    @Mock
    private InMsgService inMsgService;

    private AreaMessageProcess areaMessageProcess;

    private static final String TEST_CMD_JSON = "{\"noArea\":1,\"noJunc\":2,\"cmd\":\"test\"}";
    private static final String INVALID_JSON = "{invalid json}";
    private static final int TEST_NO_AREA = 1;
    private static final int TEST_NO_JUNC = 2;
    private static final String TEST_SIGNAL_ID = "TEST_SIGNAL_001";

    @BeforeEach
    void setUp() {
        areaMessageProcess = new AreaMessageProcess(messagePublisher, controllerService, inMsgService);
    }

    @Test
    @DisplayName("Constructor should initialize all fields correctly")
    void constructor_ShouldInitializeAllFieldsCorrectly() {
        // Assert
        assertThat(areaMessageProcess.messagePublisher).isEqualTo(messagePublisher);
        assertThat(areaMessageProcess.controllerService).isEqualTo(controllerService);
        assertThat(areaMessageProcess.inMsgService).isEqualTo(inMsgService);
    }

    @Test
    @DisplayName("setDataProcessMap should execute without errors")
    void setDataProcessMap_ShouldExecuteWithoutErrors() {
        // Act & Assert
        assertThatCode(() -> areaMessageProcess.setDataProcessMap())
                .doesNotThrowAnyException();
    }

    @Test
    @DisplayName("consumer should parse JSON and publish message when valid JSON provided")
    void consumer_WhenValidJsonProvided_ShouldParseAndPublishMessage() {
        // Arrange
        AreaMessage expectedAreaMessage = new AreaMessage();
        expectedAreaMessage.setNoArea(TEST_NO_AREA);
        expectedAreaMessage.setNoJunc(TEST_NO_JUNC);

        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            mockedJSONObject.when(() -> JSONObject.parseObject(TEST_CMD_JSON, AreaMessage.class))
                    .thenReturn(expectedAreaMessage);

            // Act
            areaMessageProcess.consumer(TEST_CMD_JSON);

            // Assert
            verify(messagePublisher).publishMessage(expectedAreaMessage);
            assertThat(expectedAreaMessage.getTimeStamp()).isPositive();
        }
    }

    @Test
    @DisplayName("consumer should handle JSON parsing exception gracefully")
    void consumer_WhenJsonParsingFails_ShouldHandleExceptionGracefully() {
        // Arrange
        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            mockedJSONObject.when(() -> JSONObject.parseObject(INVALID_JSON, AreaMessage.class))
                    .thenThrow(new RuntimeException("JSON parsing error"));

            // Act & Assert
            assertThatCode(() -> areaMessageProcess.consumer(INVALID_JSON))
                    .doesNotThrowAnyException();

            verify(messagePublisher, never()).publishMessage(any());
        }
    }

    @Test
    @DisplayName("consumer should handle null command gracefully")
    void consumer_WhenCommandIsNull_ShouldHandleGracefully() {
        // Act & Assert
        assertThatCode(() -> areaMessageProcess.consumer(null))
                .doesNotThrowAnyException();

        verify(messagePublisher, never()).publishMessage(any());
    }

    @Test
    @DisplayName("consumer should handle empty command gracefully")
    void consumer_WhenCommandIsEmpty_ShouldHandleGracefully() {
        // Act & Assert
        assertThatCode(() -> areaMessageProcess.consumer(""))
                .doesNotThrowAnyException();

        verify(messagePublisher, never()).publishMessage(any());
    }

    @Test
    @DisplayName("mqMessageProcess should process message when controller exists and is allowed")
    void mqMessageProcess_WhenControllerExistsAndAllowed_ShouldProcessMessage() {
        // Arrange
        AreaMessage areaMessage = new AreaMessage();
        areaMessage.setNoArea(TEST_NO_AREA);
        areaMessage.setNoJunc(TEST_NO_JUNC);

        ControllerBaseInfo controllerInfo = new ControllerBaseInfo();
        controllerInfo.setSignalId(TEST_SIGNAL_ID);

        when(controllerService.getSignalInfo(TEST_NO_AREA, TEST_NO_JUNC))
                .thenReturn(Optional.of(controllerInfo));
        when(controllerService.processMqMsg(TEST_SIGNAL_ID)).thenReturn(true);

        // Act
        areaMessageProcess.mqMessageProcess(areaMessage);

        // Assert
        verify(controllerService).getSignalInfo(TEST_NO_AREA, TEST_NO_JUNC);
        verify(controllerService).processMqMsg(TEST_SIGNAL_ID);
        verify(inMsgService).procMqMessage(areaMessage);
    }

    @Test
    @DisplayName("mqMessageProcess should return early when area message is null")
    void mqMessageProcess_WhenAreaMessageIsNull_ShouldReturnEarly() {
        // Act
        areaMessageProcess.mqMessageProcess(null);

        // Assert
        verify(controllerService, never()).getSignalInfo(anyInt(), anyInt());
        verify(controllerService, never()).processMqMsg(anyString());
        verify(inMsgService, never()).procMqMessage(any());
    }

    @Test
    @DisplayName("mqMessageProcess should return early when controller not found")
    void mqMessageProcess_WhenControllerNotFound_ShouldReturnEarly() {
        // Arrange
        AreaMessage areaMessage = new AreaMessage();
        areaMessage.setNoArea(TEST_NO_AREA);
        areaMessage.setNoJunc(TEST_NO_JUNC);

        when(controllerService.getSignalInfo(TEST_NO_AREA, TEST_NO_JUNC))
                .thenReturn(Optional.empty());

        // Act
        areaMessageProcess.mqMessageProcess(areaMessage);

        // Assert
        verify(controllerService).getSignalInfo(TEST_NO_AREA, TEST_NO_JUNC);
        verify(controllerService, never()).processMqMsg(anyString());
        verify(inMsgService, never()).procMqMessage(any());
    }

    @Test
    @DisplayName("mqMessageProcess should return early when controller processing not allowed")
    void mqMessageProcess_WhenControllerProcessingNotAllowed_ShouldReturnEarly() {
        // Arrange
        AreaMessage areaMessage = new AreaMessage();
        areaMessage.setNoArea(TEST_NO_AREA);
        areaMessage.setNoJunc(TEST_NO_JUNC);

        ControllerBaseInfo controllerInfo = new ControllerBaseInfo();
        controllerInfo.setSignalId(TEST_SIGNAL_ID);

        when(controllerService.getSignalInfo(TEST_NO_AREA, TEST_NO_JUNC))
                .thenReturn(Optional.of(controllerInfo));
        when(controllerService.processMqMsg(TEST_SIGNAL_ID)).thenReturn(false);

        // Act
        areaMessageProcess.mqMessageProcess(areaMessage);

        // Assert
        verify(controllerService).getSignalInfo(TEST_NO_AREA, TEST_NO_JUNC);
        verify(controllerService).processMqMsg(TEST_SIGNAL_ID);
        verify(inMsgService, never()).procMqMessage(any());
    }

    @Test
    @DisplayName("mqMessageProcess should handle controller service throwing exception")
    void mqMessageProcess_WhenControllerServiceThrowsException_ShouldHandleGracefully() {
        // Arrange
        AreaMessage areaMessage = new AreaMessage();
        areaMessage.setNoArea(TEST_NO_AREA);
        areaMessage.setNoJunc(TEST_NO_JUNC);

        when(controllerService.getSignalInfo(TEST_NO_AREA, TEST_NO_JUNC))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        assertThatThrownBy(() -> areaMessageProcess.mqMessageProcess(areaMessage))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Database error");

        verify(controllerService).getSignalInfo(TEST_NO_AREA, TEST_NO_JUNC);
        verify(inMsgService, never()).procMqMessage(any());
    }

    @Test
    @DisplayName("mqMessageProcess should handle inMsgService throwing exception")
    void mqMessageProcess_WhenInMsgServiceThrowsException_ShouldPropagateException() {
        // Arrange
        AreaMessage areaMessage = new AreaMessage();
        areaMessage.setNoArea(TEST_NO_AREA);
        areaMessage.setNoJunc(TEST_NO_JUNC);

        ControllerBaseInfo controllerInfo = new ControllerBaseInfo();
        controllerInfo.setSignalId(TEST_SIGNAL_ID);

        when(controllerService.getSignalInfo(TEST_NO_AREA, TEST_NO_JUNC))
                .thenReturn(Optional.of(controllerInfo));
        when(controllerService.processMqMsg(TEST_SIGNAL_ID)).thenReturn(true);
        doThrow(new RuntimeException("Processing error")).when(inMsgService).procMqMessage(areaMessage);

        // Act & Assert
        assertThatThrownBy(() -> areaMessageProcess.mqMessageProcess(areaMessage))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Processing error");

        verify(inMsgService).procMqMessage(areaMessage);
    }

    @Test
    @DisplayName("Class should have correct Spring annotations")
    void class_ShouldHaveCorrectSpringAnnotations() {
        // Assert
        assertThat(AreaMessageProcess.class.isAnnotationPresent(Service.class)).isTrue();
    }

    @Test
    @DisplayName("consumer method should have RabbitListener annotation")
    void consumerMethod_ShouldHaveRabbitListenerAnnotation() throws NoSuchMethodException {
        // Arrange
        Method consumerMethod = AreaMessageProcess.class.getMethod("consumer", String.class);

        // Assert
        assertThat(consumerMethod.isAnnotationPresent(RabbitListener.class)).isTrue();
        
        RabbitListener rabbitListener = consumerMethod.getAnnotation(RabbitListener.class);
        assertThat(rabbitListener.queues()).containsExactly("${global.mq.centralExchange.queue}");
    }

    @Test
    @DisplayName("mqMessageProcess method should have correct annotations")
    void mqMessageProcessMethod_ShouldHaveCorrectAnnotations() throws NoSuchMethodException {
        // Arrange
        Method mqMessageProcessMethod = AreaMessageProcess.class.getMethod("mqMessageProcess", AreaMessage.class);

        // Assert
        assertThat(mqMessageProcessMethod.isAnnotationPresent(Async.class)).isTrue();
        assertThat(mqMessageProcessMethod.isAnnotationPresent(EventListener.class)).isTrue();
        
        Async asyncAnnotation = mqMessageProcessMethod.getAnnotation(Async.class);
        assertThat(asyncAnnotation.value()).isEqualTo(GlobalConfigure.MQ_MSG_PROCESS_EXECUTOR);
    }

    @Test
    @DisplayName("consumer should set timestamp on area message")
    void consumer_ShouldSetTimestampOnAreaMessage() {
        // Arrange
        AreaMessage areaMessage = new AreaMessage();
        long beforeTime = System.currentTimeMillis();

        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            mockedJSONObject.when(() -> JSONObject.parseObject(TEST_CMD_JSON, AreaMessage.class))
                    .thenReturn(areaMessage);

            // Act
            areaMessageProcess.consumer(TEST_CMD_JSON);

            // Assert
            long afterTime = System.currentTimeMillis();
            assertThat(areaMessage.getTimeStamp()).isBetween(beforeTime, afterTime);
            verify(messagePublisher).publishMessage(areaMessage);
        }
    }

    @Test
    @DisplayName("mqMessageProcess should handle area message with zero noArea and noJunc")
    void mqMessageProcess_WhenAreaMessageHasZeroValues_ShouldStillProcess() {
        // Arrange
        AreaMessage areaMessage = new AreaMessage();
        areaMessage.setNoArea(0);
        areaMessage.setNoJunc(0);

        ControllerBaseInfo controllerInfo = new ControllerBaseInfo();
        controllerInfo.setSignalId(TEST_SIGNAL_ID);

        when(controllerService.getSignalInfo(0, 0))
                .thenReturn(Optional.of(controllerInfo));
        when(controllerService.processMqMsg(TEST_SIGNAL_ID)).thenReturn(true);

        // Act
        areaMessageProcess.mqMessageProcess(areaMessage);

        // Assert
        verify(controllerService).getSignalInfo(0, 0);
        verify(controllerService).processMqMsg(TEST_SIGNAL_ID);
        verify(inMsgService).procMqMessage(areaMessage);
    }

    @Test
    @DisplayName("mqMessageProcess should handle area message with negative noArea and noJunc")
    void mqMessageProcess_WhenAreaMessageHasNegativeValues_ShouldStillProcess() {
        // Arrange
        AreaMessage areaMessage = new AreaMessage();
        areaMessage.setNoArea(-1);
        areaMessage.setNoJunc(-1);

        ControllerBaseInfo controllerInfo = new ControllerBaseInfo();
        controllerInfo.setSignalId(TEST_SIGNAL_ID);

        when(controllerService.getSignalInfo(-1, -1))
                .thenReturn(Optional.of(controllerInfo));
        when(controllerService.processMqMsg(TEST_SIGNAL_ID)).thenReturn(true);

        // Act
        areaMessageProcess.mqMessageProcess(areaMessage);

        // Assert
        verify(controllerService).getSignalInfo(-1, -1);
        verify(controllerService).processMqMsg(TEST_SIGNAL_ID);
        verify(inMsgService).procMqMessage(areaMessage);
    }

    @Test
    @DisplayName("consumer should handle very large JSON strings")
    void consumer_WhenVeryLargeJsonString_ShouldHandleCorrectly() {
        // Arrange
        StringBuilder largeJsonBuilder = new StringBuilder("{\"noArea\":1,\"noJunc\":2,\"data\":\"");
        for (int i = 0; i < 10000; i++) {
            largeJsonBuilder.append("a");
        }
        largeJsonBuilder.append("\"}");
        String largeJson = largeJsonBuilder.toString();

        AreaMessage areaMessage = new AreaMessage();
        areaMessage.setNoArea(1);
        areaMessage.setNoJunc(2);

        try (MockedStatic<JSONObject> mockedJSONObject = mockStatic(JSONObject.class)) {
            mockedJSONObject.when(() -> JSONObject.parseObject(largeJson, AreaMessage.class))
                    .thenReturn(areaMessage);

            // Act & Assert
            assertThatCode(() -> areaMessageProcess.consumer(largeJson))
                    .doesNotThrowAnyException();

            verify(messagePublisher).publishMessage(areaMessage);
        }
    }
}