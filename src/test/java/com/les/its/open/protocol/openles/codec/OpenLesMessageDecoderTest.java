package com.les.its.open.protocol.openles.codec;

import com.les.its.open.protocol.openles.constdata.LesConstant;
import com.les.its.open.protocol.openles.message.OpenLesMessage;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import static io.netty.buffer.ByteBufUtil.hexDump;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
@SpringJUnitConfig
@Slf4j
class OpenLesMessageDecoderTest {

    private OpenLesMessageDecoder decoder;

    @Mock
    private ChannelHandlerContext ctx;

    @BeforeEach
    void setUp() {
        // Initialize decoder with same parameters as production
        decoder = new OpenLesMessageDecoder(65535,
                3, 2, -5 + 2, 0);
    }

    private void logHexDump(String message, ByteBuf buffer) {
        log.info("{} = {}", message, hexDump(buffer));
    }

    @Test
    void testDecodeWithValidSyncBytes() throws Exception {
        // Create a ByteBuf with 5 sync bytes (0xFD) followed by valid message data
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength (header length + empty body + tail)
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp

        // Add tail
        buffer.writeShortLE(0xFFFF); // parity

        // Decode the message
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);

        // Assertions
        assertNotNull(message);
        assertEquals(1, message.getLesHeader().getVersion());
        assertEquals(0, message.getLesHeader().getOption());
        assertEquals(21, message.getLesHeader().getMessageLength());
        assertEquals(1, message.getLesHeader().getMessageID_1());
        assertEquals(2, message.getLesHeader().getMessageID_2());
        assertEquals(3, message.getLesHeader().getMessageID_3());
        assertEquals(4, message.getLesHeader().getMessageID_4());
        assertEquals(5, message.getLesHeader().getSource());
        assertEquals(6, message.getLesHeader().getNoArea());
        assertEquals(7, message.getLesHeader().getNoJunc());
        assertEquals(0xFFFF, message.getLesTail().getParity());
        assertEquals(0, message.getLesBody().getBody().length);
    }

    @Test
    void testDecodeWithInsufficientSyncBytes() throws Exception {
        // Create a ByteBuf with only 3 sync bytes (insufficient)
        ByteBuf buffer = Unpooled.buffer();

        // Add only 3 sync bytes
        for (int i = 0; i < 3; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Decode should return null as sync bytes are insufficient
        Object result = decoder.decode(ctx, buffer);
        assertNull(result);
    }

    @Test
    void testDecodeWithMessageBody() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        byte[] bodyData = new byte[]{1, 2, 3, 4, 5};
        int messageLength = 21 + bodyData.length; // header length + body length + tail

        // Add header data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(messageLength); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp

        // Add body data
        buffer.writeBytes(bodyData);

        // Add tail
        buffer.writeShortLE(0xFFFF); // parity

        // Decode the message
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);

        // Assertions
        assertNotNull(message);
        assertArrayEquals(bodyData, message.getLesBody().getBody());
        assertEquals(messageLength, message.getLesHeader().getMessageLength());
    }

    @Test
    void testDecodeWithMoreSyncSequence() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 3 sync bytes, then an invalid byte, then 5 sync bytes
        for (int i = 0; i < 3; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }
        buffer.writeByte(0x00); // Invalid byte
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add minimal valid message
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // decode should return null due to interrupted sequence
        OpenLesMessage result2 = (OpenLesMessage) decoder.decode(ctx, buffer);

        assertNotNull(result2);
        assertEquals(21, result2.getLesHeader().getMessageLength());
    }

    @Test
    void testDecodeWithEmptyBuffer() throws Exception {
        ByteBuf buffer = Unpooled.buffer();
        Object result = decoder.decode(ctx, buffer);
        assertNull(result);
    }

    @Test
    void testDecodeMultipleMessages() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // First message
        // Add 5 sync bytes for first message
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header data for first message
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Second message (with body data)
        // Add 5 sync bytes for second message
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        byte[] bodyData = new byte[]{1, 2, 3, 4, 5};
        int messageLength2 = 21 + bodyData.length; // header length + body length + tail

        // Add header data for second message
        buffer.writeShortLE(2); // version
        buffer.writeByte(1); // option
        buffer.writeShortLE(messageLength2); // messageLength
        buffer.writeByte(5); // messageID_1
        buffer.writeByte(6); // messageID_2
        buffer.writeByte(7); // messageID_3
        buffer.writeByte(8); // messageID_4
        buffer.writeByte(9); // source
        buffer.writeByte(10); // noArea
        buffer.writeShortLE(11); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeBytes(bodyData); // body
        buffer.writeShortLE(0xAAAA); // parity

        // Decode first message
        OpenLesMessage message1 = (OpenLesMessage) decoder.decode(ctx, buffer);

        // Assertions for first message
        assertNotNull(message1);
        assertEquals(1, message1.getLesHeader().getVersion());
        assertEquals(0, message1.getLesHeader().getOption());
        assertEquals(21, message1.getLesHeader().getMessageLength());
        assertEquals(1, message1.getLesHeader().getMessageID_1());
        assertEquals(0xFFFF, message1.getLesTail().getParity());
        assertEquals(0, message1.getLesBody().getBody().length);

        // Decode second message
        OpenLesMessage message2 = (OpenLesMessage) decoder.decode(ctx, buffer);

        // Assertions for second message
        assertNotNull(message2);
        assertEquals(2, message2.getLesHeader().getVersion());
        assertEquals(1, message2.getLesHeader().getOption());
        assertEquals(messageLength2, message2.getLesHeader().getMessageLength());
        assertEquals(5, message2.getLesHeader().getMessageID_1());
        assertEquals(0xAAAA, message2.getLesTail().getParity());
        assertArrayEquals(bodyData, message2.getLesBody().getBody());

        // Verify no more messages
        Object message3 = decoder.decode(ctx, buffer);
        assertNull(message3);
    }

    @Test
    void testDecodeWithMixedValidAndInvalidMessages() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // First message (valid)
        // Add 5 sync bytes for first message
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header data for first message
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Second message (invalid - wrong message length)
        // Add 5 sync bytes for second message
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header data with invalid message length
        buffer.writeShortLE(2); // version
        buffer.writeByte(1); // option
        buffer.writeShortLE(10); // messageLength (invalid - too small)
        buffer.writeByte(5); // messageID_1
        buffer.writeByte(6); // messageID_2
        buffer.writeByte(7); // messageID_3
        buffer.writeByte(8); // messageID_4
        buffer.writeByte(9); // source
        buffer.writeByte(10); // noArea
        buffer.writeShortLE(11); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xAAAA); // parity

        // Third message (valid with body)
        // Add 5 sync bytes for third message
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        byte[] bodyData = new byte[]{1, 2, 3, 4, 5};
        int messageLength3 = 21 + bodyData.length; // header length + body length + tail

        // Add header data for third message
        buffer.writeShortLE(3); // version
        buffer.writeByte(2); // option
        buffer.writeShortLE(messageLength3); // messageLength
        buffer.writeByte(9); // messageID_1
        buffer.writeByte(10); // messageID_2
        buffer.writeByte(11); // messageID_3
        buffer.writeByte(12); // messageID_4
        buffer.writeByte(13); // source
        buffer.writeByte(14); // noArea
        buffer.writeShortLE(15); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeBytes(bodyData); // body
        buffer.writeShortLE(0xBBBB); // parity

        // Decode and verify first message (should be valid)
        OpenLesMessage message1 = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message1, "First message should be decoded successfully");
        assertEquals(1, message1.getLesHeader().getVersion());
        assertEquals(0, message1.getLesHeader().getOption());
        assertEquals(21, message1.getLesHeader().getMessageLength());
        assertEquals(1, message1.getLesHeader().getMessageID_1());
        assertEquals(0xFFFF, message1.getLesTail().getParity());
        assertEquals(0, message1.getLesBody().getBody().length);

        // Try to decode second message (should be skipped due to invalid length)
        OpenLesMessage message2 = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNull(message2, "Second message should be skipped due to invalid length");

        // Decode and verify third message (should be valid)
        OpenLesMessage message3 = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message3, "Third message should be decoded successfully");
        assertEquals(3, message3.getLesHeader().getVersion());
        assertEquals(2, message3.getLesHeader().getOption());
        assertEquals(messageLength3, message3.getLesHeader().getMessageLength());
        assertEquals(9, message3.getLesHeader().getMessageID_1());
        assertEquals(0xBBBB, message3.getLesTail().getParity());
        assertArrayEquals(bodyData, message3.getLesBody().getBody());

        // Verify no more messages
        Object message4 = decoder.decode(ctx, buffer);
        assertNull(message4, "No more messages should be available");
    }

    @Test
    void testDecodeWithFragmentedMessages() throws Exception {
        // 创建一个完整的消息
        ByteBuf completeBuffer = Unpooled.buffer();

        // 添加同步字节
        for (int i = 0; i < 5; i++) {
            completeBuffer.writeByte(LesConstant.START_FLAG);
        }

        // 添加消息头
        completeBuffer.writeShortLE(1); // version
        completeBuffer.writeByte(0); // option
        completeBuffer.writeShortLE(21 + 5); // messageLength (header + body + tail)
        completeBuffer.writeByte(1); // messageID_1
        completeBuffer.writeByte(2); // messageID_2
        completeBuffer.writeByte(3); // messageID_3
        completeBuffer.writeByte(4); // messageID_4
        completeBuffer.writeByte(5); // source
        completeBuffer.writeByte(6); // noArea
        completeBuffer.writeShortLE(7); // noJunc
        completeBuffer.writeLongLE(System.currentTimeMillis()); // timeStamp

        // 添加5字节的消息体
        byte[] bodyData = new byte[]{1, 2, 3, 4, 5};
        completeBuffer.writeBytes(bodyData);

        // 添加校验位
        completeBuffer.writeShortLE(0xFFFF);

        // 获取完整的字节数组
        byte[] completeMessage = new byte[completeBuffer.readableBytes()];
        completeBuffer.getBytes(0, completeMessage);

        // 第一次接收：只接收同步字节和部分头部
        ByteBuf firstPart = Unpooled.buffer();
        firstPart.writeBytes(completeMessage, 0, 10);

        // 尝试解码第一部分（应该返回null因为数据不完整）
        Object result1 = decoder.decode(ctx, firstPart);
        assertNull(result1, "Decoding incomplete message should return null");

        // 第二次接收：接收剩余的头部数据
        firstPart.writeBytes(completeMessage, 10, 11);

        // 尝试解码（应该还是返回null因为数据仍不完整）
        Object result2 = decoder.decode(ctx, firstPart);
        assertNull(result2, "Decoding incomplete message should return null");

        // 第三次接收：接收消息体数据
        firstPart.writeBytes(completeMessage, 21, 5 + 5);

        // 尝试解码（应该还是返回null因为还没有接收到校验位）
        Object result3 = decoder.decode(ctx, firstPart);
        assertNull(result3, "Decoding message without tail should return null");

        // 最后接收：接收校验位
        firstPart.writeBytes(completeMessage, 26 + 5, 2);

        // 最终解码（应该成功解码完整消息）
        OpenLesMessage finalResult = (OpenLesMessage) decoder.decode(ctx, firstPart);

        // 验证解码结果
        assertNotNull(finalResult, "Final decoding should succeed");
        assertEquals(1, finalResult.getLesHeader().getVersion());
        assertEquals(0, finalResult.getLesHeader().getOption());
        assertEquals(21 + 5, finalResult.getLesHeader().getMessageLength());
        assertEquals(1, finalResult.getLesHeader().getMessageID_1());
        assertEquals(2, finalResult.getLesHeader().getMessageID_2());
        assertEquals(3, finalResult.getLesHeader().getMessageID_3());
        assertEquals(4, finalResult.getLesHeader().getMessageID_4());
        assertEquals(5, finalResult.getLesHeader().getSource());
        assertEquals(6, finalResult.getLesHeader().getNoArea());
        assertEquals(7, finalResult.getLesHeader().getNoJunc());
        assertArrayEquals(bodyData, finalResult.getLesBody().getBody());
        assertEquals(0xFFFF, finalResult.getLesTail().getParity());

        // 验证缓冲区已经被完全读取
        assertEquals(0, firstPart.readableBytes(), "Buffer should be fully consumed");

        firstPart.release();
    }

    @Test
    void testDecodeWithFragmentedMultipleMessages() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // 第一个消息的第一部分（同步字节和部分头部）
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength

        // 尝试解码第一部分（应该返回null）
        Object result1 = decoder.decode(ctx, buffer);
        assertNull(result1, "Decoding incomplete first message should return null");

        // 添加第一个消息的剩余部分
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // 解码第一个完整消息
        OpenLesMessage message1 = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message1, "First complete message should be decoded");
        assertEquals(1, message1.getLesHeader().getVersion());
        assertEquals(21, message1.getLesHeader().getMessageLength());

        // 添加第二个消息的部分数据（带消息体）
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }
        byte[] bodyData = new byte[]{1, 2, 3, 4, 5};
        int messageLength2 = 21 + bodyData.length;

        buffer.writeShortLE(2); // version
        buffer.writeByte(1); // option
        buffer.writeShortLE(messageLength2); // messageLength
        buffer.writeByte(5); // messageID_1

        // 尝试解码不完整的第二个消息（应该返回null）
        Object result2 = decoder.decode(ctx, buffer);
        assertNull(result2, "Decoding incomplete second message should return null");

        // 添加第二个消息的剩余数据
        buffer.writeByte(6); // messageID_2
        buffer.writeByte(7); // messageID_3
        buffer.writeByte(8); // messageID_4
        buffer.writeByte(9); // source
        buffer.writeByte(10); // noArea
        buffer.writeShortLE(11); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeBytes(bodyData); // body
        buffer.writeShortLE(0xAAAA); // parity

        // 解码完整的第二个消息
        OpenLesMessage message2 = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message2, "Second complete message should be decoded");
        assertEquals(2, message2.getLesHeader().getVersion());
        assertEquals(messageLength2, message2.getLesHeader().getMessageLength());
        assertArrayEquals(bodyData, message2.getLesBody().getBody());

        buffer.release();
    }

    @Test
    void testDecodeWithLargeMessageBody() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Create large body data (6KB)
        byte[] largeBodyData = new byte[6 * 1024];
        for (int i = 0; i < largeBodyData.length; i++) {
            largeBodyData[i] = (byte) (i % 256);
        }
        int messageLength = 21 + largeBodyData.length;

        // Add header data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(messageLength); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeBytes(largeBodyData);
        buffer.writeShortLE(0xFFFF);

        // Decode message
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);

        assertNotNull(message, "Large message should be decoded successfully");
        assertEquals(messageLength, message.getLesHeader().getMessageLength());
        assertArrayEquals(largeBodyData, message.getLesBody().getBody());

        buffer.release();
    }

    @Test
    void testDecodeWithInvalidMessageLength() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header with invalid length (too large)
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(12000); // messageLength (maximum unsigned short value)
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF);

        // Decode should return null for invalid length
        Object message = decoder.decode(ctx, buffer);
        assertNull(message, "Message with invalid length should not be decoded");

        buffer.release();
    }


    @Test
    void testDecodeWithInvalidMessageLengthAndRight() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header with invalid length (too large)
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(12000); // messageLength (maximum unsigned short value)
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF);


        // First message (valid)
        // Add 5 sync bytes for first message
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header data for first message
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Decode should return null for invalid length
        Object message = decoder.decode(ctx, buffer);
        assertNull(message, "Message with invalid length should not be decoded");

        buffer.release();
    }

    @Test
    void testDecodeWithNegativeBodyLength() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header with message length smaller than header length
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(10); // messageLength (smaller than header length)
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF);

        // Decode should return null for negative body length
        Object message = decoder.decode(ctx, buffer);
        assertNull(message, "Message with negative body length should not be decoded");

        buffer.release();
    }

    @Test
    void testDecodeWithIncompleteParityBytes() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeByte(0xFF); // Only one byte of parity

        // Decode should return null for incomplete parity
        Object message = decoder.decode(ctx, buffer);
        assertNull(message, "Message with incomplete parity should not be decoded");

        buffer.release();
    }

    @Test
    void testDecodeWithInterferenceInSyncBytes() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 3 sync bytes, then interference, then 5 sync bytes
        for (int i = 0; i < 3; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }
        buffer.writeByte(0x00); // Interference byte
        buffer.writeByte(0xFF); // Interference byte
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add valid message data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity


        //  decode attempt should succeed
        OpenLesMessage result2 = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(result2, " decode after interference should succeed");
        assertEquals(1, result2.getLesHeader().getVersion());
        assertEquals(21, result2.getLesHeader().getMessageLength());

        buffer.release();
    }

    @Test
    void testDecodeWithMaximumMessageSize() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Create maximum size body (65000 bytes - header length)
        int maxBodySize = 65000 - 21;
        byte[] maxBodyData = new byte[maxBodySize];
        for (int i = 0; i < maxBodyData.length; i++) {
            maxBodyData[i] = (byte) (i % 256);
        }
        int messageLength = 21 + maxBodyData.length;

        // Add header data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(messageLength); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeBytes(maxBodyData);
        buffer.writeShortLE(0xFFFF);

        // Decode message
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);

        assertNotNull(message, "Maximum size message should be decoded successfully");
        assertEquals(messageLength, message.getLesHeader().getMessageLength());
        assertArrayEquals(maxBodyData, message.getLesBody().getBody());

        buffer.release();
    }

    @Test
    void testDecodeWithExactlyMinimumSyncBytes() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add exactly 5 sync bytes (minimum required)
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add minimal valid message
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Decode should succeed
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message, "Message with exactly 5 sync bytes should be decoded");
        assertEquals(1, message.getLesHeader().getVersion());
        assertEquals(21, message.getLesHeader().getMessageLength());

        buffer.release();
    }

    @Test
    void testDecodeWithExcessiveSyncBytes() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add more than 5 sync bytes (should still work)
        for (int i = 0; i < 10; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add valid message data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Decode should succeed
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message, "Message with excessive sync bytes should be decoded");
        assertEquals(1, message.getLesHeader().getVersion());
        assertEquals(21, message.getLesHeader().getMessageLength());

        buffer.release();
    }

    @Test
    void testDecodeWithZeroLengthBody() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header with zero-length body
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength (header only)
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Decode should succeed with empty body
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message, "Message with zero-length body should be decoded");
        assertEquals(21, message.getLesHeader().getMessageLength());
        assertEquals(0, message.getLesBody().getBody().length);

        buffer.release();
    }

    @Test
    void testDecodeWithSingleByteBody() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        byte[] singleByteBody = new byte[]{0x42};
        int messageLength = 21 + singleByteBody.length;

        // Add header data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(messageLength); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeBytes(singleByteBody);
        buffer.writeShortLE(0xFFFF);

        // Decode message
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);

        assertNotNull(message, "Message with single byte body should be decoded");
        assertEquals(messageLength, message.getLesHeader().getMessageLength());
        assertArrayEquals(singleByteBody, message.getLesBody().getBody());

        buffer.release();
    }

    @Test
    void testDecodeWithBoundaryMessageLength() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Test boundary case: message length exactly at header size
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength (exactly header size)
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Decode should succeed with empty body
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message, "Message with boundary length should be decoded");
        assertEquals(21, message.getLesHeader().getMessageLength());
        assertEquals(0, message.getLesBody().getBody().length);

        buffer.release();
    }

    @Test
    void testDecodeWithValidCRC() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add valid message with known CRC value
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // Valid CRC

        // Decode should succeed
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message, "Message with valid CRC should be decoded");
        assertEquals(0xFFFF, message.getLesTail().getParity());

        buffer.release();
    }

    @Test
    void testDecodeWithZeroCRC() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add message with zero CRC (should still work)
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0x0000); // Zero CRC

        // Decode should succeed
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message, "Message with zero CRC should be decoded");
        assertEquals(0x0000, message.getLesTail().getParity());

        buffer.release();
    }

    @Test
    void testDecodeWithAlternatingCRCPattern() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add message with alternating pattern CRC
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xAAAA); // Alternating pattern CRC

        // Decode should succeed
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message, "Message with alternating pattern CRC should be decoded");
        assertEquals(0xAAAA, message.getLesTail().getParity());

        buffer.release();
    }

    @Test
    void testDecodeWithMaximumCRCValue() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add message with maximum CRC value
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // Maximum CRC value

        // Decode should succeed
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message, "Message with maximum CRC value should be decoded");
        assertEquals(0xFFFF, message.getLesTail().getParity());

        buffer.release();
    }

    @Test
    void testDecodeWithSpecificCRCPatterns() throws Exception {
        // Test various CRC patterns to ensure they are handled correctly
        int[] crcPatterns = {0x1234, 0x5678, 0x9ABC, 0xDEF0, 0x5555, 0xAAAA, 0x0001, 0x8000};

        for (int crc : crcPatterns) {
            ByteBuf buffer = Unpooled.buffer();

            // Add 5 sync bytes
            for (int i = 0; i < 5; i++) {
                buffer.writeByte(LesConstant.START_FLAG);
            }

            // Add message with specific CRC pattern
            buffer.writeShortLE(1); // version
            buffer.writeByte(0); // option
            buffer.writeShortLE(21); // messageLength
            buffer.writeByte(1); // messageID_1
            buffer.writeByte(2); // messageID_2
            buffer.writeByte(3); // messageID_3
            buffer.writeByte(4); // messageID_4
            buffer.writeByte(5); // source
            buffer.writeByte(6); // noArea
            buffer.writeShortLE(7); // noJunc
            buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
            buffer.writeShortLE(crc); // Specific CRC pattern

            // Decode should succeed
            OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
            assertNotNull(message, "Message with CRC pattern 0x" + Integer.toHexString(crc) + " should be decoded");
            assertEquals(crc, message.getLesTail().getParity());

            buffer.release();
        }
    }

    @Test
    void testDecodeWithDifferentProtocolVersions() throws Exception {
        // Test various protocol versions
        int[] versions = {0x0001, 0x0002, 0x0100, 0x0200, 0xFFFF};

        for (int version : versions) {
            ByteBuf buffer = Unpooled.buffer();

            // Add 5 sync bytes
            for (int i = 0; i < 5; i++) {
                buffer.writeByte(LesConstant.START_FLAG);
            }

            // Add message with specific protocol version
            buffer.writeShortLE(version); // version
            buffer.writeByte(0); // option
            buffer.writeShortLE(21); // messageLength
            buffer.writeByte(1); // messageID_1
            buffer.writeByte(2); // messageID_2
            buffer.writeByte(3); // messageID_3
            buffer.writeByte(4); // messageID_4
            buffer.writeByte(5); // source
            buffer.writeByte(6); // noArea
            buffer.writeShortLE(7); // noJunc
            buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
            buffer.writeShortLE(0xFFFF); // parity

            // Decode should succeed regardless of version
            OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
            assertNotNull(message, "Message with version 0x" + Integer.toHexString(version) + " should be decoded");
            assertEquals(version, message.getLesHeader().getVersion());

            buffer.release();
        }
    }

    @Test
    void testDecodeWithDefaultProtocolVersion() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add message with default protocol version
        buffer.writeShortLE(LesConstant.DEFAULT_VERSION); // default version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Decode should succeed
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message, "Message with default protocol version should be decoded");
        assertEquals(LesConstant.DEFAULT_VERSION, message.getLesHeader().getVersion());

        buffer.release();
    }

    @Test
    void testDecodeWithZeroProtocolVersion() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add message with zero protocol version
        buffer.writeShortLE(0x0000); // zero version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Decode should succeed (version validation is not decoder's responsibility)
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message, "Message with zero protocol version should be decoded");
        assertEquals(0x0000, message.getLesHeader().getVersion());

        buffer.release();
    }

    @Test
    void testDecodeWithMaximumProtocolVersion() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add message with maximum protocol version
        buffer.writeShortLE(0xFFFF); // maximum version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Decode should succeed
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message, "Message with maximum protocol version should be decoded");
        assertEquals(0xFFFF, message.getLesHeader().getVersion());

        buffer.release();
    }

    @Test
    void testDecodeWithVariousOptionValues() throws Exception {
        // Test various option values
        int[] options = {0x00, 0x01, 0x0F, 0x10, 0xFF};

        for (int option : options) {
            ByteBuf buffer = Unpooled.buffer();

            // Add 5 sync bytes
            for (int i = 0; i < 5; i++) {
                buffer.writeByte(LesConstant.START_FLAG);
            }

            // Add message with specific option value
            buffer.writeShortLE(1); // version
            buffer.writeByte(option); // option
            buffer.writeShortLE(21); // messageLength
            buffer.writeByte(1); // messageID_1
            buffer.writeByte(2); // messageID_2
            buffer.writeByte(3); // messageID_3
            buffer.writeByte(4); // messageID_4
            buffer.writeByte(5); // source
            buffer.writeByte(6); // noArea
            buffer.writeShortLE(7); // noJunc
            buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
            buffer.writeShortLE(0xFFFF); // parity

            // Decode should succeed regardless of option value
            OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
            assertNotNull(message, "Message with option 0x" + Integer.toHexString(option) + " should be decoded");
            assertEquals(option, message.getLesHeader().getOption());

            buffer.release();
        }
    }

    @Test
    void testDecodeWithVariousMessageIDCombinations() throws Exception {
        // Test various message ID combinations
        int[][] messageIDs = {
                {0x00, 0x00, 0x00, 0x00}, // All zeros
                {0xFF, 0xFF, 0xFF, 0xFF}, // All ones
                {0x01, 0x02, 0x03, 0x04}, // Sequential
                {0x10, 0x20, 0x30, 0x40}, // Bit patterns
                {0xAA, 0xBB, 0xCC, 0xDD}, // Alternating patterns
                {0x80, 0x40, 0x20, 0x10}  // High bits
        };

        for (int[] messageID : messageIDs) {
            ByteBuf buffer = Unpooled.buffer();

            // Add 5 sync bytes
            for (int i = 0; i < 5; i++) {
                buffer.writeByte(LesConstant.START_FLAG);
            }

            // Add message with specific message ID combination
            buffer.writeShortLE(1); // version
            buffer.writeByte(0); // option
            buffer.writeShortLE(21); // messageLength
            buffer.writeByte(messageID[0]); // messageID_1
            buffer.writeByte(messageID[1]); // messageID_2
            buffer.writeByte(messageID[2]); // messageID_3
            buffer.writeByte(messageID[3]); // messageID_4
            buffer.writeByte(5); // source
            buffer.writeByte(6); // noArea
            buffer.writeShortLE(7); // noJunc
            buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
            buffer.writeShortLE(0xFFFF); // parity

            // Decode should succeed
            OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
            assertNotNull(message, "Message with IDs [" +
                    String.format("%02X", messageID[0]) + ", " +
                    String.format("%02X", messageID[1]) + ", " +
                    String.format("%02X", messageID[2]) + ", " +
                    String.format("%02X", messageID[3]) + "] should be decoded");
            assertEquals(messageID[0], message.getLesHeader().getMessageID_1());
            assertEquals(messageID[1], message.getLesHeader().getMessageID_2());
            assertEquals(messageID[2], message.getLesHeader().getMessageID_3());
            assertEquals(messageID[3], message.getLesHeader().getMessageID_4());

            buffer.release();
        }
    }

    @Test
    void testDecodeWithOrgIdCalculation() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add message with specific message IDs that form a meaningful orgId
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(0x12); // messageID_1
        buffer.writeByte(0x34); // messageID_2
        buffer.writeByte(0x56); // messageID_3
        buffer.writeByte(0x78); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Decode should succeed
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message, "Message should be decoded for orgId calculation test");

        // Verify orgId calculation: (0x12 << 24) + (0x34 << 16) + (0x56 << 8) + 0x78
        int expectedOrgId = (0x12 << 24) + (0x34 << 16) + (0x56 << 8) + 0x78;
        assertEquals(expectedOrgId, message.getLesHeader().getOrgId());

        buffer.release();
    }

    @Test
    void testDecodeWithZeroMessageIDs() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add message with all zero message IDs
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(0x00); // messageID_1
        buffer.writeByte(0x00); // messageID_2
        buffer.writeByte(0x00); // messageID_3
        buffer.writeByte(0x00); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Decode should succeed
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message, "Message with zero message IDs should be decoded");
        assertEquals(0, message.getLesHeader().getMessageID_1());
        assertEquals(0, message.getLesHeader().getMessageID_2());
        assertEquals(0, message.getLesHeader().getMessageID_3());
        assertEquals(0, message.getLesHeader().getMessageID_4());
        assertEquals(0, message.getLesHeader().getOrgId()); // All zeros should result in zero orgId

        buffer.release();
    }

    @Test
    void testDecodeWithMaximumMessageIDs() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add message with maximum message IDs
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(0xFF); // messageID_1
        buffer.writeByte(0xFF); // messageID_2
        buffer.writeByte(0xFF); // messageID_3
        buffer.writeByte(0xFF); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Decode should succeed
        OpenLesMessage message = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNotNull(message, "Message with maximum message IDs should be decoded");
        assertEquals(0xFF, message.getLesHeader().getMessageID_1());
        assertEquals(0xFF, message.getLesHeader().getMessageID_2());
        assertEquals(0xFF, message.getLesHeader().getMessageID_3());
        assertEquals(0xFF, message.getLesHeader().getMessageID_4());
        assertEquals(-1, message.getLesHeader().getOrgId()); // All 0xFF should result in -1 orgId

        buffer.release();
    }


    @Test
    void testDecodeWithMalformedMessageLength() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header with malformed message length (less than header size)
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(10); // messageLength (smaller than header size)
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Decode should return null for malformed message length
        Object result = decoder.decode(ctx, buffer);
        assertNull(result, "Message with malformed length should return null");

        buffer.release();
    }


    @Test
    void testDecodeWithReleasedBuffer() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add valid message data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // Release buffer first
        buffer.release();

        // Decode should handle released buffer gracefully
        try {
            Object result = decoder.decode(ctx, buffer);
            // Result could be null or throw exception, both are acceptable
            assertTrue(result == null || result instanceof OpenLesMessage,
                    "Decode should handle released buffer gracefully");
        } catch (Exception e) {
            // Exception is also acceptable for released buffer
            assertTrue(e instanceof IllegalStateException || e instanceof IndexOutOfBoundsException,
                    "Expected buffer-related exception for released buffer");
        }
    }

    @Test
    void testDecodeWithGarbageDataAfterSync() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add garbage data instead of valid message
        byte[] garbageData = new byte[50];
        for (int i = 0; i < garbageData.length; i++) {
            garbageData[i] = (byte) (i % 256);
        }
        buffer.writeBytes(garbageData);

        // Decode should return null for garbage data
        Object result = decoder.decode(ctx, buffer);
        assertNull(result, "Garbage data after sync should return null");

        buffer.release();
    }

    @Test
    void testDecodeWithMultipleCorruptedMessages() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // First message (corrupted)
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }
        // Add corrupted header data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(10); // invalid messageLength
        buffer.writeByte(1); // messageID_1
        // Missing rest of data

        // Second message (valid)
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        buffer.writeShortLE(0xFFFF); // parity

        // First decode should return null (corrupted message)
        Object result1 = decoder.decode(ctx, buffer);
        assertNull(result1, "First corrupted message should return null");

        // Second decode should succeed (valid message)
        OpenLesMessage message2 = (OpenLesMessage) decoder.decode(ctx, buffer);
        assertNull(message2, "Second valid message should be null");


        buffer.release();
    }

    @Test
    void testDecodeWithEdgeCaseBufferSizes() throws Exception {
        // Test various edge case buffer sizes
        int[] bufferSizes = {1, 2, 3, 4, 5, 6, 10, 15, 20, 25, 30};

        for (int size : bufferSizes) {
            ByteBuf buffer = Unpooled.buffer();

            // Add partial sync bytes based on buffer size
            for (int i = 0; i < Math.min(size, 5); i++) {
                buffer.writeByte(LesConstant.START_FLAG);
            }

            // Add some data if there's space
            if (size > 5) {
                int remaining = size - 5;
                for (int i = 0; i < remaining; i++) {
                    buffer.writeByte((byte) (i % 256));
                }
            }

            // Decode should handle various buffer sizes gracefully
            Object result = decoder.decode(ctx, buffer);
            // For small buffers, result should be null
            if (size < 5) {
                assertNull(result, "Small buffer size " + size + " should return null");
            }
            // For larger buffers, result could be null or valid message

            buffer.release();
        }
    }

    @Test
    void testDecodeWithCorruptedSyncBytes() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add corrupted sync bytes (not all 0xFD)
        buffer.writeByte(LesConstant.START_FLAG);
        buffer.writeByte(LesConstant.START_FLAG);
        buffer.writeByte(0x00); // Corrupted byte
        buffer.writeByte(LesConstant.START_FLAG);
        buffer.writeByte(LesConstant.START_FLAG);

        // Decode should return null due to corrupted sync sequence
        Object result = decoder.decode(ctx, buffer);
        assertNull(result, "Message with corrupted sync bytes should return null");

        buffer.release();
    }

    @Test
    void testDecodeWithIncompleteMessageAfterSync() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add only partial header data (insufficient for complete message)
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(21); // messageLength
        buffer.writeByte(1); // messageID_1
        // Missing rest of header and tail

        // Decode should return null due to incomplete message
        Object result = decoder.decode(ctx, buffer);
        assertNull(result, "Incomplete message after sync should return null");

        buffer.release();
    }

    @Test
    void testDecodeWithBufferUnderflow() throws Exception {
        ByteBuf buffer = Unpooled.buffer();

        // Add 5 sync bytes
        for (int i = 0; i < 5; i++) {
            buffer.writeByte(LesConstant.START_FLAG);
        }

        // Add header with message length that exceeds available data
        buffer.writeShortLE(1); // version
        buffer.writeByte(0); // option
        buffer.writeShortLE(100); // messageLength (larger than available data)
        buffer.writeByte(1); // messageID_1
        buffer.writeByte(2); // messageID_2
        buffer.writeByte(3); // messageID_3
        buffer.writeByte(4); // messageID_4
        buffer.writeByte(5); // source
        buffer.writeByte(6); // noArea
        buffer.writeShortLE(7); // noJunc
        buffer.writeLongLE(System.currentTimeMillis()); // timeStamp
        // Missing body and tail data

        // Decode should return null due to buffer underflow
        Object result = decoder.decode(ctx, buffer);
        assertNull(result, "Message with insufficient data should return null");

        buffer.release();
    }

}