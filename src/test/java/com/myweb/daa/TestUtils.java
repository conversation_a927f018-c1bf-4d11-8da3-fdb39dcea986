package com.myweb.daa;

import com.alibaba.fastjson2.JSONObject;
import com.les.its.open.area.juncer.msg.param.lookload.dto.InterGreenTime;
import com.les.its.open.area.juncer.msg.param.lookload.dto.InterGreenTimeTable;
import com.les.its.open.area.message.param.lookload.InterGreenTimeParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.IntStream;

@Slf4j
public class TestUtils {


    public static Locale getLocaleByLanguage(String language) {
        Locale locale = new Locale("zh", "CN");
        switch (language.toLowerCase().trim()){
            case "zh_hk" : locale =  new Locale("zh", "HK"); break;
            case "en_gb" : locale =  new Locale("en", "GB"); break;
            case "zh_cn" : locale =  new Locale("zh", "CN"); break;
            default : break;
        }
        return locale;
    }


    public static void main(String[] args) {

        Integer data = null;
        Integer data2 = (Integer) data;

        Locale locale =  getLocaleByLanguage("zh_HK");
        log.error("data2={}, locale={}", data2, locale);

        long time = System.currentTimeMillis();
        byte[] bytes = longToBytes(time);
        StringBuilder littleEndianTime = new StringBuilder();
        for (byte b : bytes) {
            littleEndianTime.append(String.format("%02x", b));
        }
        log.error("current Time in little-endian order = 0x{}", littleEndianTime.toString());
    }

    private static byte[] longToBytes(long x) {
        byte[] bytes = new byte[8];
        for (int i = 0; i < 8; i++) {
            bytes[i] = (byte) (x & 0xFF);
            x >>= 8;
        }
        return bytes;
    }


    @Test
    public void print(){


        InterGreenTimeParam interGreenTimeParam = new InterGreenTimeParam();

        List<InterGreenTimeTable> interGreenTimeTables = new ArrayList<>();
        interGreenTimeParam.setInterGreenTimeTables(interGreenTimeTables);

        IntStream.rangeClosed(1, 4).forEach(
                index -> {
                    InterGreenTimeTable interGreenTimeTable = new InterGreenTimeTable();
                    interGreenTimeTable.setInterGreenTimeTableNo(index);
                    interGreenTimeTables.add(interGreenTimeTable);

                    List<InterGreenTime> interGreenTimes = new ArrayList<>();
                    interGreenTimeTable.setInterGreenTimes(interGreenTimes);

                    IntStream.rangeClosed(1, 64).forEach(
                            phaseNo -> {
                                InterGreenTime interGreenTime = new InterGreenTime();
                                interGreenTime.setPhaseNo(phaseNo);
                                List<Integer> interGreenTimeSeq = new ArrayList<>();
                                interGreenTime.setInterGreenTimeSeq(interGreenTimeSeq);
                                IntStream.rangeClosed(1, 64).forEach(
                                        greenTime -> {
                                            interGreenTimeSeq.add(5);
                                        }
                                );
                                interGreenTimes.add(interGreenTime);
                            }
                    );

                }
        );

        List<Integer> modeTrans = new ArrayList<>();
        interGreenTimeParam.setModeTrans(modeTrans);
        IntStream.rangeClosed(1, 255).forEach(
                data -> modeTrans.add(1)
        );


        log.error("模拟数据-{}", JSONObject.toJSONString(interGreenTimeParam));

    }

}
