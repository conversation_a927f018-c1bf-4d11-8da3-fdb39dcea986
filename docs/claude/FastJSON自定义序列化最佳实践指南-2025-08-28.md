# FastJSON自定义序列化最佳实践指南

**创建时间**: 2025-08-28  
**项目**: OpenLES交通信号控制系统  
**用途**: Plan对象及其引用对象的FastJSON序列化优化

## 背景

OpenLES系统中Plan对象作为核心的交通信号方案配置，包含复杂的嵌套结构和验证注解。需要针对Plan对象及其引用对象实现高效且灵活的JSON序列化机制。

## 当前项目中的FastJSON使用情况

### 1. 版本使用
- **FastJSON1**: `com.alibaba.fastjson.annotation.JSONField` (P1049Entity.java)
- **FastJSON2**: `com.alibaba.fastjson2.annotation.JSONField` (主要使用)

### 2. 现有最佳实践
项目中已有很好的实现模式：

#### 日志过滤器模式 (LogPropertyFilter.java)
```java
@Service
public class LogPropertyFilter {
    private final PropertyFilter propertyFilter;
    
    public LogPropertyFilter(){
        propertyFilter = new PropertyFilter() {
            @Override
            public boolean apply(Object object, String name, Object value) {
                if(juncerConfigure.getDisableFields() == null ||
                        juncerConfigure.getDisableFields().isEmpty()){
                    return true;
                }
                if(juncerConfigure.getDisableFields().contains(name)){
                    return false;
                }
                return true;
            }
        };
    }
}
```

#### 日志序列化使用
```java
// JuncerUtils.java:55 & OutMsgService.java:151
JSON.toJSONString(tabInBase, logPropertyFilter.getPropertyFilter())
```

#### JSONField注解控制
```java
// 排除字段序列化
@JSONField(serialize=false)
private transient MsgType msgType;

// 日期格式化
@JSONField(format = "yyyy-MM-dd HH:mm:ss")
private Date createTime;
```

## Plan对象自定义序列化最佳实践

### 1. Plan对象结构分析

#### Plan.java (DTO层)
```java
public class Plan implements DataIndexAble {
    @NotNull @Range(min = 1, max = 128)
    private Integer planNo;
    
    @NotNull @Range(min = 1, max = 8)
    private Integer crossingSeqNo;
    
    @NotNull @Range(min = 1, max = 65535)
    private Integer cycle;
    
    @NotNull @Range(min = 1, max = 16)
    private Integer coordinatedStageSeq;
    
    @NotNull @Range(min = 0, max = 2)
    private Integer coordinatedRef;
    
    @NotNull @Range(min =0, max = 65535)
    private Integer offset;
    
    @NotNull @Range(min = 1, max = 16)
    private Integer stageNumber;
    
    @NotNull @Size(min = 16, max = 16) @Valid
    private List<PlanStageParam> planStageParams;
    
    @NotNull
    private String planName;
}
```

#### PlanParam.java (消息层)
```java
public class PlanParam implements SgpTransAble {
    // 类似字段，但使用@Min/@Max注解
    // 包含国际化消息配置
    // 包含signalControllerID字段
}
```

### 2. 自定义序列化策略

#### 策略1: 基于注解的细粒度控制
```java
public class Plan implements DataIndexAble {
    // 1. 控制敏感字段序列化
    @JSONField(serialize = false)
    private String internalId;
    
    // 2. 自定义字段名
    @JSONField(name = "plan_no")
    private Integer planNo;
    
    // 3. 条件序列化
    @JSONField(serialize = true, deserialize = true)
    private String planName;
    
    // 4. 自定义序列化顺序
    @JSONField(ordinal = 1)
    private Integer planNo;
    
    @JSONField(ordinal = 2)
    private String planName;
    
    // 5. 嵌套对象序列化配置
    @JSONField(serialize = true)
    private List<PlanStageParam> planStageParams;
}
```

#### 策略2: 自定义PropertyFilter
```java
@Component
public class PlanSerializationFilter {
    
    /**
     * 基础序列化过滤器 - 排除验证注解信息
     */
    public PropertyFilter getBasicFilter() {
        return (object, name, value) -> {
            // 排除null值
            if (value == null) return false;
            
            // 排除内部字段
            if (name.startsWith("_") || name.startsWith("internal")) {
                return false;
            }
            
            return true;
        };
    }
    
    /**
     * 详细序列化过滤器 - 包含所有字段
     */
    public PropertyFilter getDetailFilter() {
        return (object, name, value) -> true;
    }
    
    /**
     * 简化序列化过滤器 - 仅核心字段
     */
    public PropertyFilter getSimpleFilter() {
        Set<String> coreFields = Set.of(
            "planNo", "planName", "cycle", "stageNumber"
        );
        return (object, name, value) -> coreFields.contains(name);
    }
    
    /**
     * 网络传输过滤器 - 排除大对象
     */
    public PropertyFilter getNetworkFilter() {
        return (object, name, value) -> {
            // 排除大的嵌套集合用于网络传输
            if (name.equals("planStageParams") && 
                value instanceof List && 
                ((List<?>) value).size() > 10) {
                return false;
            }
            return true;
        };
    }
}
```

#### 策略3: 自定义ObjectSerializer
```java
public class PlanObjectSerializer implements ObjectSerializer {
    
    @Override
    public void write(JSONWriter jsonWriter, Object object, Object fieldName, 
                     Type fieldType, long features) {
        Plan plan = (Plan) object;
        
        jsonWriter.startObject();
        
        // 核心信息
        jsonWriter.writeName("planNo");
        jsonWriter.writeInt32(plan.getPlanNo());
        
        jsonWriter.writeName("planName");
        jsonWriter.writeString(plan.getPlanName());
        
        jsonWriter.writeName("cycle");
        jsonWriter.writeInt32(plan.getCycle());
        
        // 协调信息
        if (plan.getCoordinatedStageSeq() != null && plan.getCoordinatedStageSeq() > 0) {
            jsonWriter.writeName("coordination");
            jsonWriter.startObject();
            jsonWriter.writeName("stageSeq");
            jsonWriter.writeInt32(plan.getCoordinatedStageSeq());
            jsonWriter.writeName("ref");
            jsonWriter.writeInt32(plan.getCoordinatedRef());
            jsonWriter.writeName("offset");
            jsonWriter.writeInt32(plan.getOffset());
            jsonWriter.endObject();
        }
        
        // 阶段信息 - 压缩表示
        if (plan.getPlanStageParams() != null && !plan.getPlanStageParams().isEmpty()) {
            jsonWriter.writeName("stages");
            jsonWriter.startArray();
            for (PlanStageParam stage : plan.getPlanStageParams()) {
                // 自定义阶段序列化逻辑
                writeStageParam(jsonWriter, stage);
            }
            jsonWriter.endArray();
        }
        
        jsonWriter.endObject();
    }
    
    private void writeStageParam(JSONWriter jsonWriter, PlanStageParam stage) {
        // 实现阶段参数的压缩序列化
    }
}
```

#### 策略4: SerializeConfig配置
```java
@Configuration
public class FastJsonConfiguration {
    
    @Bean
    public SerializeConfig planSerializeConfig() {
        SerializeConfig config = new SerializeConfig();
        
        // 注册Plan对象的自定义序列化器
        config.put(Plan.class, new PlanObjectSerializer());
        config.put(PlanParam.class, new PlanParamObjectSerializer());
        config.put(PlanStageParam.class, new PlanStageParamObjectSerializer());
        
        return config;
    }
    
    @Bean
    public JSONWriter.Feature[] defaultFeatures() {
        return new JSONWriter.Feature[]{
            JSONWriter.Feature.WriteMapNullValue,      // 写入null值
            JSONWriter.Feature.PrettyFormat,           // 格式化输出
            JSONWriter.Feature.WriteEnumsUsingName,    // 枚举使用名称
            JSONWriter.Feature.NotWriteDefaultValue    // 不写入默认值
        };
    }
}
```

### 3. 使用场景和实现方案

#### 场景1: 日志记录
```java
@Service
public class PlanLogService {
    
    @Autowired
    private PlanSerializationFilter planFilter;
    
    public void logPlanOperation(Plan plan, String operation) {
        String planJson = JSON.toJSONString(plan, 
            planFilter.getBasicFilter(),
            JSONWriter.Feature.PrettyFormat);
            
        IPBasedLogger.logMessage(getIP(), getAreaNo(), getJuncNo(),
            "Plan {} - {}", operation, planJson);
    }
}
```

#### 场景2: 网络传输
```java
@RestController
public class PlanController {
    
    @Autowired
    private PlanSerializationFilter planFilter;
    
    @GetMapping("/plans/{planNo}")
    public String getPlan(@PathVariable Integer planNo) {
        Plan plan = planService.getPlan(planNo);
        
        // 网络传输时使用压缩序列化
        return JSON.toJSONString(plan, 
            planFilter.getNetworkFilter(),
            JSONWriter.Feature.NotWriteDefaultValue);
    }
    
    @GetMapping("/plans/{planNo}/detail") 
    public String getPlanDetail(@PathVariable Integer planNo) {
        Plan plan = planService.getPlan(planNo);
        
        // 详细信息使用完整序列化
        return JSON.toJSONString(plan, 
            planFilter.getDetailFilter(),
            JSONWriter.Feature.PrettyFormat);
    }
}
```

#### 场景3: 数据存储
```java
@Service
public class PlanStorageService {
    
    public void savePlanToCache(Plan plan) {
        // 缓存存储使用紧凑格式
        String compactJson = JSON.toJSONString(plan,
            JSONWriter.Feature.NotWriteDefaultValue,
            JSONWriter.Feature.WriteEnumsUsingName);
            
        redisTemplate.opsForValue().set("plan:" + plan.getPlanNo(), compactJson);
    }
    
    public void exportPlanToFile(Plan plan) {
        // 文件导出使用可读格式
        String readableJson = JSON.toJSONString(plan,
            JSONWriter.Feature.PrettyFormat,
            JSONWriter.Feature.WriteMapNullValue);
            
        Files.writeString(Paths.get("plans/" + plan.getPlanNo() + ".json"), 
                         readableJson);
    }
}
```

### 4. 性能优化建议

#### 复用SerializeConfig
```java
@Component
public class PlanSerializationManager {
    
    private static final SerializeConfig BASIC_CONFIG = new SerializeConfig();
    private static final SerializeConfig NETWORK_CONFIG = new SerializeConfig();
    private static final SerializeConfig DETAIL_CONFIG = new SerializeConfig();
    
    static {
        // 预配置不同场景的序列化配置
        BASIC_CONFIG.put(Plan.class, new BasicPlanSerializer());
        NETWORK_CONFIG.put(Plan.class, new NetworkPlanSerializer());
        DETAIL_CONFIG.put(Plan.class, new DetailPlanSerializer());
    }
    
    public String toBasicJson(Plan plan) {
        return JSON.toJSONString(plan, BASIC_CONFIG);
    }
    
    public String toNetworkJson(Plan plan) {
        return JSON.toJSONString(plan, NETWORK_CONFIG);
    }
    
    public String toDetailJson(Plan plan) {
        return JSON.toJSONString(plan, DETAIL_CONFIG);
    }
}
```

#### 字段级优化
```java
public class Plan implements DataIndexAble {
    
    // 使用原始类型减少装箱
    @JSONField(ordinal = 1)
    private int planNo;
    
    // 延迟加载大对象
    @JSONField(serialize = false)
    private transient List<PlanStageParam> planStageParams;
    
    // 提供自定义getter用于序列化
    @JSONField(name = "stages")
    public Object getStagesForSerialization() {
        if (planStageParams == null || planStageParams.isEmpty()) {
            return null;
        }
        // 返回压缩的阶段信息
        return planStageParams.stream()
            .map(this::compressStageParam)
            .collect(Collectors.toList());
    }
}
```

## 集成建议

### 1. 配置管理
继承现有的JuncerConfigure配置模式：
```java
@ConfigurationProperties(prefix = "juncer.serialization")
@Data
public class SerializationConfigure {
    
    /**
     * Plan序列化禁用字段
     */
    private List<String> planDisableFields = new ArrayList<>();
    
    /**
     * 网络传输时的最大阶段数量
     */
    private int maxStagesForNetwork = 10;
    
    /**
     * 是否启用压缩序列化
     */
    private boolean enableCompression = true;
    
    /**
     * 序列化模式：basic, network, detail
     */
    private String defaultMode = "basic";
}
```

### 2. 测试建议
```java
@SpringBootTest
class PlanSerializationTest {
    
    @Test
    void testBasicSerialization() {
        Plan plan = createTestPlan();
        String json = JSON.toJSONString(plan, basicFilter);
        
        assertThat(json).doesNotContain("internalId");
        assertThat(json).contains("planNo", "planName");
    }
    
    @Test
    void testNetworkSerialization() {
        Plan plan = createLargePlan(); // 超过10个阶段
        String json = JSON.toJSONString(plan, networkFilter);
        
        // 验证大对象被压缩
        assertThat(json.length()).isLessThan(NETWORK_SIZE_LIMIT);
    }
    
    @Test 
    void testSerializationPerformance() {
        Plan plan = createTestPlan();
        
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        
        for (int i = 0; i < 10000; i++) {
            JSON.toJSONString(plan, basicFilter);
        }
        
        stopWatch.stop();
        assertThat(stopWatch.getTotalTimeMillis()).isLessThan(1000);
    }
}
```

## 总结

基于OpenLES项目现有的FastJSON使用模式，建议采用以下最佳实践：

1. **继承现有模式**: 复用LogPropertyFilter的PropertyFilter模式
2. **分场景配置**: 为不同使用场景（日志、网络、存储）配置不同的序列化策略
3. **注解优先**: 优先使用@JSONField注解进行简单控制
4. **性能优化**: 对于复杂对象使用自定义ObjectSerializer
5. **配置驱动**: 通过配置文件控制序列化行为

这样既保持了与现有代码的一致性，又能满足Plan对象复杂序列化需求。