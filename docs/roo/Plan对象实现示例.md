# Plan对象自定义实现示例

## 1. 增强的Plan类实现

```java
package com.les.its.open.area.juncer.msg.param.lookload.dto.enhanced;

import com.alibaba.fastjson2.annotation.JSONField;
import com.les.its.open.bussiness.bean.DataIndexAble;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EnhancedPlan implements DataIndexAble {
    
    @JSONField(name = "plan_no", ordinal = 1)
    private Integer planNo;
    
    @JSONField(name = "plan_name", ordinal = 2)
    private String planName;
    
    @JSONField(ordinal = 3)
    private Integer cycle;
    
    @JSONField(name = "stage_params", ordinal = 4)
    private List<PlanStageParam> planStageParams;
    
    @JSONField(format = "yyyy-MM-dd HH:mm:ss", ordinal = 5)
    private LocalDateTime createTime;
    
    // 不参与序列化的字段
    @JSONField(serialize = false)
    private String internalFlag;
    
    // 只输出不输入的字段
    @JSONField(serialize = true, deserialize = false, name = "total_stage_time")
    private Integer totalStageTime;
    
    @Override
    public int getDataNo() {
        return planNo != null ? planNo : 0;
    }
    
    public static class EnhancedPlanBuilder {
        public EnhancedPlanBuilder withBasicInfo(Integer planNo, String planName, Integer cycle) {
            this.planNo = planNo;
            this.planName = planName;
            this.cycle = cycle;
            this.createTime = LocalDateTime.now();
            return this;
        }
        
        public EnhancedPlanBuilder addStage(PlanStageParam stage) {
            if (this.planStageParams == null) {
                this.planStageParams = new ArrayList<>();
            }
            this.planStageParams.add(stage);
            return this;
        }
        
        public EnhancedPlan buildAndValidate() {
            if (this.planStageParams != null) {
                this.totalStageTime = this.planStageParams.stream()
                    .mapToInt(stage -> stage.getStageTime() != null ? stage.getStageTime() : 0)
                    .sum();
            }
            
            EnhancedPlan plan = this.build();
            
            if (plan.getPlanNo() == null || plan.getPlanNo() <= 0) {
                throw new IllegalArgumentException("方案编号必须大于0");
            }
            
            return plan;
        }
    }
}
```

## 2. JSON序列化服务

```java
package com.les.its.open.area.juncer.msg.param.lookload.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.filter.PropertyFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@Service
@Slf4j
public class PlanJsonService {
    
    private final JSONWriter.Context defaultContext;
    private final JSONWriter.Context compactContext;
    private final PlanPropertyFilter propertyFilter;
    
    public PlanJsonService() {
        this.defaultContext = JSONWriter.Context.of(
            JSONWriter.Feature.PrettyFormat,
            JSONWriter.Feature.WriteNulls
        );
        this.defaultContext.setDateFormat("yyyy-MM-dd HH:mm:ss");
        
        this.compactContext = JSONWriter.Context.of();
        this.compactContext.setDateFormat("yyyy-MM-dd HH:mm:ss");
        
        this.propertyFilter = new PlanPropertyFilter();
    }
    
    public String serialize(EnhancedPlan plan) {
        try {
            return JSON.toJSONString(plan, defaultContext);
        } catch (Exception e) {
            log.error("Plan对象序列化失败: {}", plan, e);
            throw new RuntimeException("序列化失败: " + e.getMessage(), e);
        }
    }
    
    public String serializeCompact(EnhancedPlan plan) {
        try {
            return JSON.toJSONString(plan, propertyFilter, compactContext);
        } catch (Exception e) {
            log.error("Plan对象紧凑序列化失败: {}", plan, e);
            throw new RuntimeException("紧凑序列化失败: " + e.getMessage(), e);
        }
    }
    
    public String serializeBatch(List<EnhancedPlan> plans) {
        try {
            return JSON.toJSONString(plans, propertyFilter, defaultContext);
        } catch (Exception e) {
            log.error("Plan对象批量序列化失败，数量: {}", plans.size(), e);
            throw new RuntimeException("批量序列化失败: " + e.getMessage(), e);
        }
    }
    
    public EnhancedPlan deserialize(String json) {
        try {
            return JSON.parseObject(json, EnhancedPlan.class);
        } catch (Exception e) {
            log.error("Plan对象反序列化失败: {}", json, e);
            throw new RuntimeException("反序列化失败: " + e.getMessage(), e);
        }
    }
    
    private static class PlanPropertyFilter implements PropertyFilter {
        @Override
        public boolean apply(Object object, String name, Object value) {
            if ("internalFlag".equals(name)) {
                return false;
            }
            
            if (value instanceof Collection && ((Collection<?>) value).isEmpty()) {
                return false;
            }
            
            return true;
        }
    }
}
```

## 3. 增强的消息发送服务

```java
package com.les.its.open.transport.enhanced;

import com.les.its.open.area.juncer.msg.param.lookload.dto.enhanced.EnhancedPlan;
import com.les.its.open.area.juncer.msg.param.lookload.service.PlanJsonService;
import com.les.its.open.transport.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class EnhancedPlanMessageSender {
    
    @Autowired
    private MessageSender messageSender;
    
    @Autowired
    private PlanJsonService planJsonService;
    
    public void sendPlan(String routeKey, EnhancedPlan plan) {
        try {
            validatePlan(plan);
            String jsonMessage = planJsonService.serializeCompact(plan);
            
            log.info("发送Plan消息 - routeKey: {}, planNo: {}, size: {} bytes", 
                routeKey, plan.getPlanNo(), jsonMessage.getBytes().length);
            
            messageSender.send(routeKey, jsonMessage);
            
        } catch (Exception e) {
            log.error("发送Plan消息失败 - routeKey: {}, planNo: {}", 
                routeKey, plan != null ? plan.getPlanNo() : "null", e);
            throw new RuntimeException("消息发送失败: " + e.getMessage(), e);
        }
    }
    
    public void sendPlanNats(String controllerId, String routeKey, int noArea, int noJunc, EnhancedPlan plan) {
        try {
            validatePlan(plan);
            String jsonMessage = planJsonService.serializeCompact(plan);
            
            log.info("发送Plan消息到NATS - controllerId: {}, routeKey: {}, planNo: {}", 
                controllerId, routeKey, plan.getPlanNo());
            
            messageSender.sendNats(controllerId, routeKey, noArea, noJunc, jsonMessage);
            
        } catch (Exception e) {
            log.error("发送Plan消息到NATS失败 - controllerId: {}, planNo: {}", 
                controllerId, plan != null ? plan.getPlanNo() : "null", e);
            throw new RuntimeException("NATS消息发送失败: " + e.getMessage(), e);
        }
    }
    
    public void sendPlans(String routeKey, List<EnhancedPlan> plans) {
        if (plans == null || plans.isEmpty()) {
            log.warn("批量发送Plan消息 - 列表为空");
            return;
        }
        
        try {
            for (EnhancedPlan plan : plans) {
                validatePlan(plan);
            }
            
            String jsonMessage = planJsonService.serializeBatch(plans);
            
            log.info("批量发送Plan消息 - routeKey: {}, count: {}, size: {} bytes", 
                routeKey, plans.size(), jsonMessage.getBytes().length);
            
            messageSender.send(routeKey + ".batch", jsonMessage);
            
        } catch (Exception e) {
            log.error("批量发送Plan消息失败 - routeKey: {}, count: {}", 
                routeKey, plans.size(), e);
            throw new RuntimeException("批量消息发送失败: " + e.getMessage(), e);
        }
    }
    
    private void validatePlan(EnhancedPlan plan) {
        if (plan == null) {
            throw new IllegalArgumentException("Plan对象不能为空");
        }
        
        if (plan.getPlanNo() <= 0) {
            throw new IllegalArgumentException("方案编号必须大于0");
        }
        
        if (plan.getPlanStageParams() == null || plan.getPlanStageParams().isEmpty()) {
            throw new IllegalArgumentException("阶段参数不能为空");
        }
    }
}
```

## 4. 使用示例

```java
@Component
@Slf4j
public class PlanUsageExample {
    
    @Autowired
    private PlanJsonService planJsonService;
    
    @Autowired
    private EnhancedPlanMessageSender messageSender;
    
    public EnhancedPlan createSamplePlan() {
        return EnhancedPlan.builder()
            .withBasicInfo(1, "主干道高峰方案", 120)
            .addStage(createStageParam(1, 30))
            .addStage(createStageParam(2, 25))
            .addStage(createStageParam(3, 35))
            .addStage(createStageParam(4, 30))
            .buildAndValidate();
    }
    
    public void demonstrateJsonSerialization() {
        EnhancedPlan plan = createSamplePlan();
        
        String standardJson = planJsonService.serialize(plan);
        log.info("标准序列化结果:\n{}", standardJson);
        
        String compactJson = planJsonService.serializeCompact(plan);
        log.info("紧凑序列化结果: {}", compactJson);
        
        EnhancedPlan deserializedPlan = planJsonService.deserialize(standardJson);
        log.info("反序列化成功，方案编号: {}", deserializedPlan.getPlanNo());
    }
    
    public void demonstrateMessageSending() {
        EnhancedPlan plan = createSamplePlan();
        
        messageSender.sendPlan("traffic.plan.update", plan);
        messageSender.sendPlanNats("controller_001", "plan.config", 1, 1, plan);
    }
    
    private PlanStageParam createStageParam(int stageNo, int stageTime) {
        return PlanStageParam.builder()
            .stageNo(stageNo)
            .stageTime(stageTime)
            .stageActivationType(1)
            .coordinatedForceOff(1)
            .build();
    }
}
```

## 5. 配置类

```java
@Configuration
public class PlanConfiguration {
    
    @Bean
    @Primary
    public JSONWriter.Context globalJsonWriterContext() {
        JSONWriter.Context context = JSONWriter.Context.of(
            JSONWriter.Feature.PrettyFormat,
            JSONWriter.Feature.WriteNulls
        );
        
        context.setDateFormat("yyyy-MM-dd HH:mm:ss");
        context.setZoneId(ZoneId.of("Asia/Shanghai"));
        
        return context;
    }
    
    @Bean
    public Validator validator() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        return factory.getValidator();
    }
}
```