# Plan对象自定义与Fastjson最佳实践指南

## 1. 概述

本文档基于OpenLES项目中的`Plan`对象，提供了自定义Plan对象以及使用Fastjson的`JSON.toJSONString`方法的最佳实践建议。

### 1.1 技术栈版本
- **Fastjson2**: 2.0.56
- **Spring Boot**: 3.3.6 
- **Java**: 17

### 1.2 当前Plan对象结构
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Plan implements DataIndexAble {
    private Integer planNo;              // 方案编号 [1,128]
    private Integer crossingSeqNo;       // 子路口号 [1,8]
    private Integer cycle;               // 周期 [1,65535]
    private Integer coordinatedStageSeq; // 协调序号 [1,16]
    private Integer coordinatedRef;      // 协调参考点 [0,2]
    private Integer offset;              // 相位差 [0,65535]
    private Integer stageNumber;         // 阶段个数 [1,16]
    private List<PlanStageParam> planStageParams; // 阶段信息
    private String planName;             // 方案名称
}
```

## 2. Plan对象自定义最佳实践

### 2.1 JSON序列化自定义

#### 2.1.1 使用@JSONField注解控制序列化
```java
import com.alibaba.fastjson2.annotation.JSONField;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Plan implements DataIndexAble {
    
    @JSONField(name = "plan_no", ordinal = 1)
    private Integer planNo;
    
    @JSONField(name = "crossing_seq_no", ordinal = 2)
    private Integer crossingSeqNo;
    
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    // 不参与序列化的字段
    @JSONField(serialize = false)
    private String internalFlag;
    
    // 条件序列化
    @JSONField(serialize = true, deserialize = false)
    private String outputOnlyField;
}
```

#### 2.1.2 自定义序列化器
```java
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.writer.ObjectWriter;

public class PlanObjectWriter implements ObjectWriter<Plan> {
    @Override
    public void write(JSONWriter writer, Object object, Object fieldName, Type fieldType, long features) {
        if (object == null) {
            writer.writeNull();
            return;
        }
        
        Plan plan = (Plan) object;
        writer.startObject();
        
        // 自定义字段序列化逻辑
        writer.writeName("planNo");
        writer.writeInt32(plan.getPlanNo());
        
        writer.writeName("planName");
        writer.writeString(plan.getPlanName());
        
        // 自定义复杂对象序列化
        writer.writeName("stageInfo");
        writer.startObject();
        writer.writeName("stageCount");
        writer.writeInt32(plan.getStageNumber());
        writer.writeName("stages");
        writer.writeAny(plan.getPlanStageParams());
        writer.endObject();
        
        writer.endObject();
    }
}

// 注册自定义序列化器
JSONWriter.Context context = JSONWriter.Context.of(JSONWriter.Feature.PrettyFormat);
context.setObjectWriter(Plan.class, new PlanObjectWriter());
```

### 2.2 验证约束增强

#### 2.2.1 自定义验证注解
```java
import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PlanBusinessValidator.class)
@Documented
public @interface ValidPlan {
    String message() default "Plan对象业务逻辑验证失败";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}

public class PlanBusinessValidator implements ConstraintValidator<ValidPlan, Plan> {
    @Override
    public boolean isValid(Plan plan, ConstraintValidatorContext context) {
        if (plan == null) return true;
        
        // 业务逻辑验证
        if (plan.getStageNumber() != null && plan.getPlanStageParams() != null) {
            // 验证阶段数量与实际阶段列表的一致性
            long validStageCount = plan.getPlanStageParams().stream()
                .filter(stage -> stage.getStageNo() != null && stage.getStageNo() > 0)
                .count();
                
            if (validStageCount != plan.getStageNumber()) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("阶段数量与实际阶段列表不一致")
                       .addConstraintViolation();
                return false;
            }
        }
        
        return true;
    }
}

// 在Plan类上使用
@ValidPlan
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Plan implements DataIndexAble {
    // ... 字段定义
}
```

### 2.3 构建器模式增强

#### 2.3.1 增强的Builder模式
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Plan implements DataIndexAble {
    // ... 字段定义
    
    public static class PlanBuilder {
        // 添加便捷的构建方法
        public PlanBuilder withBasicInfo(Integer planNo, String planName, Integer cycle) {
            this.planNo = planNo;
            this.planName = planName;
            this.cycle = cycle;
            return this;
        }
        
        public PlanBuilder withCoordination(Integer stageSeq, Integer ref, Integer offset) {
            this.coordinatedStageSeq = stageSeq;
            this.coordinatedRef = ref;
            this.offset = offset;
            return this;
        }
        
        public PlanBuilder addStage(PlanStageParam stage) {
            if (this.planStageParams == null) {
                this.planStageParams = new ArrayList<>();
            }
            this.planStageParams.add(stage);
            this.stageNumber = this.planStageParams.size();
            return this;
        }
        
        // 验证构建
        public Plan buildAndValidate() {
            Plan plan = this.build();
            
            // 执行基本验证
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            Validator validator = factory.getValidator();
            Set<ConstraintViolation<Plan>> violations = validator.validate(plan);
            
            if (!violations.isEmpty()) {
                StringBuilder sb = new StringBuilder("Plan对象验证失败: ");
                violations.forEach(v -> sb.append(v.getMessage()).append("; "));
                throw new IllegalArgumentException(sb.toString());
            }
            
            return plan;
        }
    }
}
```

## 3. Fastjson JSON.toJSONString最佳实践

### 3.1 基础使用模式

#### 3.1.1 简单序列化
```java
// 基础序列化
Plan plan = Plan.builder()
    .planNo(1)
    .planName("测试方案")
    .cycle(120)
    .build();

String json = JSON.toJSONString(plan);
```

#### 3.1.2 带特性的序列化
```java
// 美化输出
String prettyJson = JSON.toJSONString(plan, JSONWriter.Feature.PrettyFormat);

// 包含null值
String jsonWithNull = JSON.toJSONString(plan, JSONWriter.Feature.WriteNulls);

// 排序字段
String sortedJson = JSON.toJSONString(plan, JSONWriter.Feature.MapSortField);

// 组合特性
String customJson = JSON.toJSONString(plan, 
    JSONWriter.Feature.PrettyFormat,
    JSONWriter.Feature.WriteNulls,
    JSONWriter.Feature.MapSortField
);
```

### 3.2 高级序列化配置

#### 3.2.1 全局配置
```java
@Configuration
public class FastjsonConfig {
    
    @Bean
    @Primary
    public JSONWriter.Context jsonWriterContext() {
        JSONWriter.Context context = JSONWriter.Context.of(
            JSONWriter.Feature.PrettyFormat,
            JSONWriter.Feature.WriteNulls,
            JSONWriter.Feature.MapSortField
        );
        
        // 设置日期格式
        context.setDateFormat("yyyy-MM-dd HH:mm:ss");
        
        // 设置时区
        context.setZoneId(ZoneId.of("Asia/Shanghai"));
        
        return context;
    }
}
```

#### 3.2.2 自定义过滤器
```java
public class PlanPropertyFilter implements PropertyFilter {
    @Override
    public boolean apply(Object object, String name, Object value) {
        // 过滤敏感信息
        if ("internalFlag".equals(name)) {
            return false;
        }
        
        // 过滤空集合
        if (value instanceof Collection && ((Collection<?>) value).isEmpty()) {
            return false;
        }
        
        // 过滤零值
        if (value instanceof Integer && ((Integer) value) == 0) {
            return false;
        }
        
        return true;
    }
}

// 使用过滤器
String filteredJson = JSON.toJSONString(plan, new PlanPropertyFilter());
```

### 3.3 序列化性能优化

#### 3.3.1 对象复用
```java
@Component
public class PlanJsonService {
    
    // 复用JSONWriter实例
    private final ThreadLocal<JSONWriter> writerThreadLocal = 
        ThreadLocal.withInitial(() -> JSONWriter.of());
    
    public String toJsonString(Plan plan) {
        JSONWriter writer = writerThreadLocal.get();
        try {
            writer.reset();  // 重置writer状态
            writer.writeAny(plan);
            return writer.toString();
        } finally {
            writer.reset();  // 清理状态
        }
    }
}
```

#### 3.3.2 批量序列化优化
```java
public class PlanBatchJsonService {
    
    public String toJsonArray(List<Plan> plans) {
        if (plans == null || plans.isEmpty()) {
            return "[]";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        
        for (int i = 0; i < plans.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(JSON.toJSONString(plans.get(i)));
        }
        
        sb.append("]");
        return sb.toString();
    }
    
    // 流式处理大量数据
    public void writeJsonStream(List<Plan> plans, OutputStream output) throws IOException {
        try (JSONWriter writer = JSONWriter.of(output, StandardCharsets.UTF_8)) {
            writer.startArray();
            
            for (int i = 0; i < plans.size(); i++) {
                if (i > 0) {
                    writer.writeComma();
                }
                writer.writeAny(plans.get(i));
            }
            
            writer.endArray();
            writer.flush();
        }
    }
}
```

## 4. 项目集成建议

### 4.1 服务层封装

#### 4.1.1 Plan序列化服务
```java
@Service
@Slf4j
public class PlanSerializationService {
    
    private final JSONWriter.Context context;
    private final PlanPropertyFilter propertyFilter;
    
    public PlanSerializationService() {
        this.context = JSONWriter.Context.of(
            JSONWriter.Feature.PrettyFormat,
            JSONWriter.Feature.WriteNulls
        );
        this.context.setDateFormat("yyyy-MM-dd HH:mm:ss");
        this.propertyFilter = new PlanPropertyFilter();
    }
    
    /**
     * 标准序列化
     */
    public String serialize(Plan plan) {
        try {
            return JSON.toJSONString(plan, context);
        } catch (Exception e) {
            log.error("Plan对象序列化失败: {}", plan, e);
            throw new RuntimeException("序列化失败", e);
        }
    }
    
    /**
     * 过滤序列化
     */
    public String serializeFiltered(Plan plan) {
        try {
            return JSON.toJSONString(plan, propertyFilter, context);
        } catch (Exception e) {
            log.error("Plan对象过滤序列化失败: {}", plan, e);
            throw new RuntimeException("过滤序列化失败", e);
        }
    }
    
    /**
     * 紧凑序列化（用于网络传输）
     */
    public String serializeCompact(Plan plan) {
        try {
            JSONWriter.Context compactContext = JSONWriter.Context.of();
            return JSON.toJSONString(plan, propertyFilter, compactContext);
        } catch (Exception e) {
            log.error("Plan对象紧凑序列化失败: {}", plan, e);
            throw new RuntimeException("紧凑序列化失败", e);
        }
    }
}
```

### 4.2 消息发送集成

#### 4.2.1 增强的MessageSender
```java
@Component
@Slf4j
public class EnhancedMessageSender {
    
    @Autowired
    private MessageSender messageSender;
    
    @Autowired
    private PlanSerializationService planSerializationService;
    
    /**
     * 发送Plan对象消息
     */
    public void sendPlan(String routeKey, Plan plan) {
        try {
            // 验证Plan对象
            validatePlan(plan);
            
            // 序列化
            String jsonMessage = planSerializationService.serializeCompact(plan);
            
            // 记录发送日志
            log.debug("发送Plan消息 - routeKey: {}, planNo: {}, size: {}", 
                routeKey, plan.getPlanNo(), jsonMessage.length());
            
            // 发送消息
            messageSender.send(routeKey, jsonMessage);
            
        } catch (Exception e) {
            log.error("发送Plan消息失败 - routeKey: {}, plan: {}", routeKey, plan, e);
            throw new RuntimeException("消息发送失败", e);
        }
    }
    
    private void validatePlan(Plan plan) {
        if (plan == null) {
            throw new IllegalArgumentException("Plan对象不能为空");
        }
        if (plan.getPlanNo() == null) {
            throw new IllegalArgumentException("方案编号不能为空");
        }
        // 其他验证逻辑...
    }
}
```

## 5. 错误处理与日志

### 5.1 序列化异常处理
```java
@ControllerAdvice
public class JsonSerializationExceptionHandler {
    
    @ExceptionHandler(JSONException.class)
    public ResponseEntity<ErrorResponse> handleJsonException(JSONException e) {
        log.error("JSON序列化异常: ", e);
        
        ErrorResponse error = ErrorResponse.builder()
            .code("JSON_SERIALIZATION_ERROR")
            .message("数据序列化失败")
            .timestamp(LocalDateTime.now())
            .build();
            
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }
}
```

### 5.2 日志配置建议
```yaml
# logback-spring.xml 配置片段
<logger name="com.les.its.open.area.juncer.msg.param.lookload.dto.Plan" level="DEBUG"/>
<logger name="com.alibaba.fastjson2" level="WARN"/>

# 序列化性能监控
<logger name="PlanSerialization" level="INFO" additivity="false">
    <appender-ref ref="PLAN_SERIALIZATION_FILE"/>
</logger>
```

## 6. 单元测试建议

### 6.1 Plan对象测试
```java
@ExtendWith(MockitoExtension.class)
class PlanSerializationTest {
    
    @Test
    @DisplayName("Plan对象序列化测试")
    void testPlanSerialization() {
        // 构建测试数据
        Plan plan = Plan.builder()
            .planNo(1)
            .planName("测试方案")
            .cycle(120)
            .coordinatedStageSeq(1)
            .coordinatedRef(0)
            .offset(0)
            .stageNumber(2)
            .crossingSeqNo(1)
            .planStageParams(createTestStageParams())
            .build();
        
        // 序列化
        String json = JSON.toJSONString(plan);
        
        // 验证
        assertThat(json).isNotNull();
        assertThat(json).contains("\"planNo\":1");
        assertThat(json).contains("\"planName\":\"测试方案\"");
        
        // 反序列化验证
        Plan deserializedPlan = JSON.parseObject(json, Plan.class);
        assertThat(deserializedPlan).isEqualTo(plan);
    }
    
    @Test
    @DisplayName("序列化性能测试")
    void testSerializationPerformance() {
        Plan plan = createLargePlan();
        
        long startTime = System.nanoTime();
        for (int i = 0; i < 1000; i++) {
            JSON.toJSONString(plan);
        }
        long endTime = System.nanoTime();
        
        long durationMs = (endTime - startTime) / 1_000_000;
        assertThat(durationMs).isLessThan(1000); // 1000次序列化应在1秒内完成
    }
}
```

## 7. 总结

### 7.1 关键要点
1. **使用@JSONField注解**：控制字段序列化行为
2. **自定义序列化器**：处理复杂序列化逻辑
3. **性能优化**：复用JSONWriter实例，使用流式处理
4. **错误处理**：完善的异常处理机制
5. **验证增强**：结合Jakarta Bean Validation

### 7.2 注意事项
1. 避免在序列化过程中修改对象状态
2. 注意循环引用问题
3. 大对象序列化时考虑内存使用
4. 在高并发场景下注意线程安全
5. 定期监控序列化性能

### 7.3 项目特定建议
基于OpenLES项目的特点：
- Plan对象作为核心配置对象，序列化频繁，建议使用对象池优化
- 网络传输时使用紧凑序列化，日志记录时使用美化输出
- 结合现有的MessageSender进行集成
- 利用项目的验证框架增强数据完整性检查