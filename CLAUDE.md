# CLAUDE.md

本文件为Claude Code (claude.ai/code)在此代码库中工作时提供指导。

## 项目概述

OpenLES是一个高性能交通信号控制系统，实现了用于城市交通管理的LES（莱斯）协议。该系统使用基于TCP的自定义网络协议，在交通控制中心和路口信号控制器之间提供实时通信，支持多种交通控制命令和状态监控功能。

## 技术栈

- **Java 17** 配合 Spring Boot 3.x 框架
- **Netty 4.1.119.Final** 用于高性能异步网络通信
- **自定义OpenLES协议** 用于交通信号通信
- **RabbitMQ** 用于消息队列和事件处理
- **MySQL/H2 2.3.232** 配合 Spring Data JPA 进行数据持久化
- **Redis/Redisson 3.45.1** 用于缓存和分布式锁
- **Groovy 4.0.26** 规则引擎用于动态交通控制逻辑
- **Bucket4j 8.14.0** 用于分布式限流
- **FastJSON/FastJSON2 2.0.56-2.0.57** 用于JSON处理
- **Orika 1.5.4** 用于对象映射
- **Spring State Machine 4.0.0** 用于工作流管理
- **SpringDoc OpenAPI** 用于API文档
- **Spring Boot Admin 3.3.6** 用于应用监控
- **JUnit 5 + Mockito** 用于测试
- **Google Guava 33.4.8** 用于工具库

## 核心架构

### 包结构

#### **`area/`** - 核心交通信号通信层
- **`area.juncer/`** - Juncer协议实现
  - **`api/`** - 信号控制操作的REST API控制器
    - `AllLookSignalController` - 批量参数查询操作
    - `CmdController` - 命令执行和管理
    - `LoadSignalController` - 参数加载操作
    - `LookSignalController` - 单个参数查询
  - **`bean/`** - 核心数据结构和常量
    - `ControllerAgent` - 信号控制器代理表示
    - `ControllerStatus` - 控制器状态跟踪
    - `OpenLesConst` - 系统常量和枚举
  - **`msg/`** - 消息定义和数据结构
    - **`cmd/`** - 命令消息（45+种命令类型）
      - 相位控制、紧急优先、检测器控制
      - 计划命令、阶段控制、事务管理
    - **`link/`** - 链路建立和通信消息
    - **`param/`** - 参数查询和加载消息
    - **`status/`** - 状态报告和监控消息
  - **`proc/`** - 业务流程服务
    - `ControllerAgentService` - 代理生命周期管理

- **`area.message/`** - 消息处理和通信
  - **`api/`** - 额外的REST端点
    - `AllLookNatslController` - 基于NATS的查询操作
    - `DistributedLockController` - 锁管理API
    - `RateLimitController` - 限流控制
  - **`dbsave/`** - 数据库持久化层
    - `DataEntity` - 数据实体定义
    - `SgpDbSaveEntityProcess` - 实体持久化处理
  - **`handler/`** - 消息处理处理器
    - **`cmd/`** - 命令消息处理器（17+个处理器）
    - **`lookload/`** - 参数处理器（17+种参数类型）
    - **`status/`** - 状态消息处理器
  - **`log/`** - 基于信号的日志基础设施
  - **`mq/`** - 消息队列集成
  - **`param/`** - 参数消息定义
  - **`service/`** - 核心业务服务
    - **`distributelock/`** - 使用Redis的分布式锁
    - **`ratelimit/`** - 使用Bucket4j的限流
  - **`utils/`** - 消息处理工具

- **`area.net/`** - 网络协议处理
  - **`log/`** - 基于IP的日志系统
  - **`msg/`** - 消息转换和处理
    - **`cmd/`** - 命令消息处理器（25+个处理器）
    - **`link/`** - 链路协议处理器
    - **`param/`** - 参数消息处理器
    - **`status/`** - 状态消息处理器
  - **`proc/`** - 网络处理服务
    - `InquireService` - 系统查询服务
    - `TabCmdService` - 命令处理
    - `TabLoadService` - 参数加载
    - `TabLookService` - 参数查询

#### **`bussiness/`** - 业务逻辑层
- **`bean/`** - 业务数据结构
  - **`rtt/`** - 往返时间监控
    - `ConcurrentSlidingWindow` - 性能监控
    - `TimestampedData` - 时间序列数据处理
- **`process/`** - 业务数据处理
  - `LinkLogProcess` - 通信链路日志
  - `P1049InfosProcess` - P1049标准处理
  - `RttLogProcess` - RTT数据处理
- **`repository/`** - JPA数据仓库
- **`service/`** - 业务服务
  - **`rtt/`** - RTT监控和分析服务
- **`utils/`** - 业务工具

#### **`config/`** - 配置管理
- `GlobalConfigure` - 全局应用配置
- `ITSSocketConfigure` - 网络套接字配置
- `JuncerConfigure` - Juncer协议配置
- `RedissonConfig` - Redis分布式缓存配置
- `MessageSourceConfig` - 国际化支持

#### **`event/`** - 事件驱动架构
- **`AckManager/`** - 确认管理系统
  - `InvokeCallback` - 异步回调处理
  - `InvokeFuture` - 基于Future的确认
  - **`response/`** - 响应消息管理
- `MessageAckManager` - 中央确认协调器
- `MessagePublisher` - 事件发布基础设施
- 使用专用线程池的异步消息处理

#### **`front/`** - Web界面层
- **`controller/`** - REST API控制器
  - `DashBoardController` - 系统仪表板
  - `DataVueController` - Vue.js前端集成
  - `LinkLogController` - 通信日志
  - `NatsCmdController` - NATS命令接口
  - `RttController` - RTT监控API
  - **`dto/`** - 数据传输对象
- **`interceptor/`** - 安全和请求处理
  - `XTokenInterceptor` - 基于令牌的身份验证
  - `GlobalExceptionHandler` - 集中错误处理
- **`websocket/`** - 实时通信
  - `MessageHandler` - WebSocket消息处理
  - **`service/`** - 用于实时监控的WebSocket服务

#### **`groovy/`** - 动态规则引擎
- `GroovyCompiler` - 动态代码编译
- `SelfDefinedRuleManager` - 自定义规则管理
- `RuleInterface` - 规则执行接口

#### **`monitor/`** - 系统监控
- `ChannelMonitor` - 网络通道监控
- `HtSignalMonitor` - 信号设备监控
- `MessageProcessMonitor` - 消息处理指标

#### **`netty/`** - 网络通信基础设施
- `NettyServer` / `NettyClient` - TCP通信
- `NettyUdp` - UDP通信支持
- **`link/`** - 连接管理
- **`reconnect/`** - 自动重连处理
- **`stop/`** - 优雅关闭管理

#### **`protocol/`** - 多协议支持
- **`common/`** - 协议抽象层
  - **`channelhandler/`** - Netty通道处理器
  - **`codec/`** - 消息编码/解码
  - **`message/`** - 抽象消息定义
- **`openles/`** - LES协议实现
  - **`bean/`** - 交通控制数据结构
  - **`codec/`** - LES消息编解码器
  - **`message/`** - LES消息格式

#### **`task/`** - 异步任务管理
- `AsyncTaskConfig` - 线程池配置
- `AsyncTaskService` - 任务执行服务
- `GroovyRunner` - 规则引擎任务运行器

#### **`transport/`** - 消息传输层
- `MessageSender` - 消息传递服务
- `NatsMqConfiguration` - NATS消息配置
- `SignalConfiguration` - 信号特定传输

#### **`utils/`** - 通用工具
- `Crc16Utils` - CRC校验和计算
- `IPUtils` - 网络工具
- `TimerHolder` - 定时器管理
- `UtcTimeUtils` - 时间处理工具

### 关键架构模式

1. **命令-确认模式**：所有交通控制命令都需要信号控制器的确认以确保可靠运行
2. **事件驱动架构**：使用专用线程池和异步处理实现高性能消息处理
3. **多协议支持**：可插拔的协议处理器允许不同的通信协议
4. **基于IP和信号的日志记录**：用于调试交通控制操作的综合日志系统

## 开发命令

```bash
# 构建项目
mvn clean compile

# 运行测试（JUnit 5配合Mockito）
mvn test

# 创建JAR包
mvn clean package

# 构建Docker镜像
mvn clean package docker:build

# 本地运行应用
mvn spring-boot:run

# 运行特定测试类
mvn test -Dtest=ClassNameTest

# 使用特定配置文件运行测试
mvn test -Dspring.profiles.active=test
```

## 配置

### 应用配置文件
- **`dev`** - 使用H2数据库的开发环境
- **`test`** - 测试环境配置
- **`prod`** - 使用MySQL和Redis的生产环境

### 关键配置文件
- **`application.yml`** - 主要的Spring Boot配置（端口：19001）
- **`application-dev.yml`** - 开发环境特定设置
- **`application-prod.yml`** - 生产环境数据库和消息配置

### 核心配置项
- **数据库连接**：默认使用H2文件数据库（`./db/areasignal`）
- **RabbitMQ**：***************:5672（消息队列）
- **Redis**：***************:6379（缓存和分布式锁）
- **日志路径**：`./daa-log`（滚动日志，7天保留）
- **区域配置**：`global.areaNo: 12`（区域编号）
- **协议端口**：`its.server.local.listens.port: 7301`（OpenLES协议监听）
- **测试信号机**：支持最多3个测试信号机配置
- **国际化**：默认简体中文（zh_CN），支持繁体中文和英文

## 数据库架构

系统使用JPA实体存储交通控制数据：
- 信号控制器注册和状态
- 命令历史和确认
- RTT监控数据
- 事件日志和审计跟踪

## 协议实现

### OpenLES协议特性
- 用于高效通信的自定义二进制消息格式
- 用于连接监控的心跳机制
- 命令队列和重试逻辑
- 支持多种信号控制器类型
- CRC16校验确保数据完整性
- 分布式锁和限流保障系统稳定性

### 消息类型
- **控制命令**（相位控制、计划切换、紧急优先等45+种）
- **参数操作**（查看、加载各类配置参数）
- **状态监控**（实时状态、设备状态、故障报警）
- **链路管理**（连接建立、心跳保活、断线重连）

### 关键功能模块
- **系统步进控制**：支持手动步进和自动步进控制
- **可变配置支持**：支持可变灯组、相位、阶段个数
- **实时状态推送**：灯故障检测、相位状态、阶段状态实时推送
- **RTT监控**：网络通信质量监控和分析
- **分布式特性**：多实例部署支持分布式锁和限流

## 测试策略

### 单元测试
- 使用JUnit 5和Mockito进行服务层测试
- 使用嵌入式测试服务器进行协议处理器测试
- 使用测试数据进行业务逻辑验证

### 集成测试
- 端到端协议通信测试
- 使用测试容器的数据库集成
- 消息队列集成测试

## 监控和日志

### 日志配置
- **基于IP的日志**：每个信号控制器IP对应单独的日志文件
- **基于信号的日志**：按信号控制器ID组织
- **应用日志**：使用logback的标准Spring Boot日志

### 性能监控
- 网络性能的RTT监控
- 消息吞吐量和队列深度监控
- 连接池和资源使用跟踪

## 开发指南

### 代码组织
- 遵循Spring Boot服务层分离的最佳实践
- 对所有组件交互使用依赖注入
- 为网络操作实现适当的异常处理
- 保持协议变更的向后兼容性

### 协议开发
- 所有协议变更必须保持向后兼容性
- 新消息类型需要版本协商支持
- 使用多种控制器类型测试协议变更
- 在`docs/`目录中记录协议规范

### 数据库变更
- 使用Flyway或Liquibase进行架构迁移
- 使用类似生产环境的数据量测试迁移
- 维护交通控制数据的引用完整性
- 考虑架构变更的性能影响

## 运维考虑

### 部署
- 应用打包为可执行JAR
- Docker支持容器化部署
- 不同环境的外化配置
- 实现健康检查和就绪探针

### 可扩展性
- 基于Netty的架构支持高连接数
- Redis集群用于分布式缓存
- RabbitMQ集群用于消息队列可扩展性
- 数据库连接池和优化

### 安全性
- 在需要时通过安全通道进行网络通信
- API访问的身份验证和授权（X-Token机制）
- 所有协议消息的输入验证和数据校验
- 关键操作的审计日志和操作跟踪
- CRC16校验确保协议数据完整性
- 分布式限流防止系统过载

## 最新更新说明

### 近期功能更新（基于最新提交）
- **系统步进命令支持**：增强了手动控制能力
- **可变参数支持**：支持灯组、相位、阶段个数的动态配置
- **方案参数优化**：方案参数和日计划参数的修改和优化
- **实时状态推送**：灯故障调试、实时相位、阶段状态的变更推送
- **控制链路调试**：改进了控制链路的调试和监控功能

### 架构优化
- 采用自定义Maven Parent：`common-parent 3.0-SNAPSHOT`
- Docker镜像优化：基于JRE 17的轻量化部署
- 日志系统优化：基于IP和信号ID的分层日志记录
- 监控增强：Spring Boot Admin 3.3.6集成，支持应用健康监控

### 特別提醒
- 不要运行mvn命令,用户手动运行 
- 每次添加新功能后，请在 docs/claude 下面将新增功能最后的总结写到文件夹下面，文件名要精准说明需求，并添加时间
